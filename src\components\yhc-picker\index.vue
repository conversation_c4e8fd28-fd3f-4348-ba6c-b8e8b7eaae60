<template>
  <div class="wrapper" :class="{ 'required-field': config.required }">
    <van-field v-model="data.value" is-link :name="config.key" :label="config.label" :type="config.type"
      :placeholder="config.placeholder + config.label" :required="config.required" :rules="config.rules"
      :disabled="config.disabled" readonly input-align="left" @update:model-value="update" @click="onClick" />
    <van-popup v-model:show="showPopup" :round="config.popup.round" :position="config.popup.position"
      :style="config.popup.style" :closeable="config.popup.closeable">
      <!-- <div class="top-block">
        <span @click="showPopup = false" style="color: #007fff">取消</span>
        <span>{{ config.label }}</span>
        <span @click="onConfirm" style="color: #007fff">确认</span>
      </div> -->
      <div class="top-block" v-if="config.opts.multiple">
        <span @click="showPopup = false" style="color: #007fff">取消</span>
        <span>{{ config.label }}</span>
        <span @click="onConfirm" style="color: #007fff">确认</span>
      </div>
      <div class="top-block single-title" v-else>
        <span></span>
        <span class="title-text">{{ config.label }}</span>
        <van-icon @click="showPopup = false" name="cross" size="18" color="#969799" />
      </div>

      <div class="list-content">
        <!-- 多选模式 -->
        <template v-if="config.opts.multiple">
          <div class="add-button-container" v-if="config.addButton.show && config.addButton.position === 'top'"
            @click="onAddButtonClick">
            <div class="add-button">
              <img src="/img/checkbox.svg" alt="新增" class="add-icon" />
              <span>{{ config.addButton.text }}</span>
            </div>
          </div>
          <div v-for="(item, i) in list" :key="'multi-' + (item.id || i)" class="list-item multiple-mode"
            @click="onListItemClick(item)">
            <div class="checkbox-icon">
              <div class="checkbox" :class="{ 'checked': item.select }">
                <van-icon v-if="item.select" name="success" size="12" color="#fff" />
              </div>
            </div>
            <div class="item-content">
              <div class="item-title">{{ item[config.opts.text_key] }}</div>
            </div>
          </div>
        </template>

        <!-- 单选模式 -->
        <template v-else>
          <div class="add-button-container" v-if="config.addButton.show && config.addButton.position === 'top'"
            @click="onAddButtonClick">
            <div class="add-button" >
              <img src="/img/checkbox.svg" alt="新增" class="add-icon" />
              <span>{{ config.addButton.text }}</span>
            </div>
          </div>
          <div v-for="(item, i) in list" :key="'single-' + (item.id || i)" class="list-item single-mode"
            @click="onListItemClick(item)">
            <div class="item-content">
              <div class="item-title">{{ item[config.opts.text_key] }}</div>
            </div>
            <div class="check-icon">
              <van-icon v-if="item.select" name="success" size="16" color="#007fff" />
            </div>
          </div>
        </template>
      </div>
    </van-popup>

    <!-- 添加表单弹窗 -->
    <van-popup v-model:show="showAddPopup" :round="config.addForm.popup.round" :position="config.addForm.popup.position"
      :style="config.addForm.popup.style" :closeable="config.addForm.popup.closeable">
      <div class="add-form-container">
        <div class="add-form-header">
          <span>{{ config.addForm.title }}</span>
        </div>

        <div class="add-form-content">
          <van-form @submit="onAddConfirm">
            <template v-for="(field, index) in config.addForm.form" :key="field.key || index">
              <!-- 输入框 -->
              <van-field v-if="field.type === 'input' || field.type === 'text'" v-model="addFormData[field.key]"
                :name="field.key" :label="field.label" :placeholder="field.placeholder || `请输入${field.label}`"
                :required="field.required" :rules="field.rules" :disabled="addFormLoading" />

              <!-- 文本域 -->
              <van-field v-else-if="field.type === 'textarea'" v-model="addFormData[field.key]" :name="field.key"
                :label="field.label" :placeholder="field.placeholder || `请输入${field.label}`" :required="field.required"
                :rules="field.rules" :disabled="addFormLoading" type="textarea" :rows="field.rows || 3" />

              <!-- 数字输入框 -->
              <van-field v-else-if="field.type === 'number'" v-model="addFormData[field.key]" :name="field.key"
                :label="field.label" :placeholder="field.placeholder || `请输入${field.label}`" :required="field.required"
                :rules="field.rules" :disabled="addFormLoading" type="number" />
            </template>
          </van-form>
        </div>

        <div class="add-form-footer">
          <span @click="onAddCancel" style="color: #007fff">取消</span>
          <span @click="onAddConfirm" style="color: #007fff" :class="{ disabled: addFormLoading }">
            {{ addFormLoading ? '提交中...' : '确认' }}
          </span>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script setup>
import {
  shallowReactive,
  ref,
  getCurrentInstance,
  watch,
  triggerRef,
  nextTick
} from "vue";
import { deepAssign, handleFormData } from "@/untils";
import { showToast } from "vant";
import { useRouter } from "vue-router";
const router = useRouter();
const { proxy } = getCurrentInstance();

let config = {
  // 基础配置
  label: "下拉选择",        // 字段标签 (字符串) - 显示在表单项左侧的标签文字
  type: "text",            // 字段类型 (字符串) - 表单项类型，通常为"text"
  key: "",                 // 字段名 (字符串) - 表单数据中的字段名，如"category_id"
  placeholder: "请选择",    // 占位符 (字符串) - 未选择时显示的提示文字
  required: false,         // 是否必填 (布尔值) - true: 必填字段, false: 可选字段
  disabled: false,         // 是否禁用 (布尔值) - true: 禁用选择, false: 可正常选择
  default: '',             // 默认值 (字符串/数组) - 字段的默认选中值
  rules: [],               // 验证规则 (数组) - 表单验证规则配置

  // 弹窗配置
  popup: {
    round: true,           // 是否圆角弹窗 (布尔值) - true: 圆角样式, false: 直角样式
    position: "bottom",    // 弹窗位置 (字符串) - "bottom": 底部弹出, "center": 居中, "right": 右侧
    style: { height: "50vh", overflow: "hidden" }, // 弹窗样式 (对象) - CSS样式对象
    closeable: false,      // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
  },

  // 卡片显示配置 (暂未使用)
  card: {
    slotMap: {},           // 插槽映射 (对象) - 自定义插槽配置
    title: "title",        // 标题字段 (字符串) - 卡片标题对应的数据字段名
  },

  // 选项配置 - 核心配置项
  opts: {
    url: "",               // 数据接口地址 (字符串) - 例: "/api/options" - 获取选项数据的API
    postData: {},          // 请求参数 (对象) - 发送给接口的参数，如: {category: 1, status: 'active'}
    merge: false,          // 是否合并数据 (布尔值) - true: 合并新旧数据, false: 替换数据
    multiple: true,        // 是否多选 (布尔值) - true: 支持多选, false: 单选模式
    text_key: "title",     // 显示字段名 (字符串) - 选项显示文字对应的数据字段，如"name", "title"
    contrast_key: "id",    // 值字段名 (字符串) - 选项值对应的数据字段，如"id", "value"
    keyMap: {},            // 字段映射 (对象) - 数据字段映射配置，用于数据转换
    defaultList: [],       // 默认选项列表 (数组) - 本地默认选项数据，当API无数据时使用
  },
  // 添加按钮配置 - 允许用户动态添加新选项
  addButton: {
    show: false,           // 是否显示添加按钮 (布尔值) - true: 显示添加按钮, false: 隐藏
    text: "添加",          // 按钮文字 (字符串) - 添加按钮显示的文本
    icon: "plus",          // 按钮图标 (字符串) - 按钮图标名称，如"plus", "add"
    position: "top",       // 按钮位置 (字符串) - "top": 列表顶部, "bottom": 列表底部
  },

  // 添加表单配置 - 添加新选项时的表单配置
  addForm: {
    title: "添加选项",     // 弹窗标题 (字符串) - 添加表单弹窗的标题文字
    form: [],             // 表单字段配置 (数组) - 表单项配置，格式与yhc-form组件一致
    popup: {
      round: true,        // 是否圆角弹窗 (布尔值) - true: 圆角样式, false: 直角样式
      position: "center", // 弹窗位置 (字符串) - "center": 居中显示, "bottom": 底部弹出
      style: { width: "90vw", maxWidth: "400px", maxHeight: "70vh", overflow: "hidden" }, // 弹窗样式 (对象)
      closeable: false,   // 是否显示关闭按钮 (布尔值) - true: 显示X按钮, false: 不显示
    },
  },

  // 添加接口配置 - 提交新选项到服务器的配置
  addApi: {
    url: "",              // 添加接口地址 (字符串) - 例: "/api/add-option" - 提交新选项的API
    method: "POST",       // 请求方法 (字符串) - "POST": POST请求, "PUT": PUT请求
    postData: {},         // 额外请求参数 (对象) - 提交时的额外参数，如: {category_id: 1}
    refreshAfterAdd: true, // 添加成功后是否刷新列表 (布尔值) - true: 自动刷新, false: 不刷新
    successCallback: null, // 成功回调函数 (函数) - 添加成功后的自定义回调
    errorCallback: null,   // 错误回调函数 (函数) - 添加失败后的自定义回调
  },

  // TODO: 计划任务联动配置 - 专门用于计划任务模块的联动功能
  scheduledTaskLinkage: {
    enabled: false,        // 是否启用计划任务联动功能 (布尔值) - true: 启用联动, false: 禁用
    type: '',             // 联动类型 (字符串) - 'restaurant': 餐厅, 'meal_time': 餐时, 'stall': 档口
    parentKey: '',        // 父级字段key (字符串) - 依赖的上级字段名，如'restaurant'
    parentPostKey: ''     // 传递给接口的参数key (字符串) - 发送给API的参数名，如'dininghall_id'
  },
};
const props = defineProps({
  config: Object,
  form: Object,
});
props.config && deepAssign(config, props.config);
let emit = defineEmits(["onConfirm"]);
const data = shallowReactive({
  value: "",
  loading: false,
  finished: false,
  error: false,
  page: {
    page: 1,
    per_page: 20,
  },
});
let isListItemClick = false;
let showPopup = ref(false);
let showAddPopup = ref(false); // 添加表单弹窗显示状态
let list = ref([]);
let selectLs = [];
let addFormData = ref({}); // 添加表单数据
let addFormLoading = ref(false); // 添加表单提交loading状态
var onConfirm = () => {
  showPopup.value = false;
  selectLs = list.value.filter((el) => el.select);
  setTimeout(() => {
    emit("onConfirm", {
      data: props.form[config.key],
    });
    if (!selectLs.length) {
      props.form[config.key] = [];
      setText([]);
    }
  });
  if (props.config.LinkKeyConfig) {
    setTimeout(handleLinkKey, 20);
  }
};
// 添加按钮点击事件
var onAddButtonClick = () => {
  if(config.addButton.routeUrl){
    router.push(config.addButton.routeUrl)
    return
  }else{
  // 先关闭选择器弹窗
  showPopup.value = false;

  // 初始化表单数据
  addFormData.value = {};
  config.addForm.form.forEach(field => {
    addFormData.value[field.key] = field.default || '';
  });

  // 延迟打开添加表单弹窗，确保选择器弹窗完全关闭
  setTimeout(() => {
    showAddPopup.value = true;
  }, 100);
  }
};

// 添加表单取消
var onAddCancel = () => {
  showAddPopup.value = false;
  addFormData.value = {};

  // 延迟重新打开选择器弹窗
  setTimeout(() => {
    showPopup.value = true;
  }, 100);
};
// 添加表单确认
var onAddConfirm = async () => {
  if (addFormLoading.value) return;

  // 简单验证
  const requiredFields = config.addForm.form.filter(field => field.required);
  for (let field of requiredFields) {
    if (!addFormData.value[field.key]) {
      showToast(`请填写${field.label}`);
      return;
    }
  }

  if (!config.addApi.url) {
    showToast('未配置添加接口');
    return;
  }

  addFormLoading.value = true;

  try {
    // 准备提交数据
    const postData = { ...addFormData.value, ...config.addApi.postData };

    // 调用添加接口
    const res = await proxy.$post(config.addApi.url, postData);

    if (res.code === 200) {
      showToast('添加成功');

      // 处理新添加的数据项
      if (res.data) {
        const newItem = handleNewAddedItem(res.data);
        // 将新项添加到列表中
        list.value.push(newItem);
      }

      // 执行成功回调
      if (config.addApi.successCallback && typeof config.addApi.successCallback === 'function') {
        config.addApi.successCallback(res.data);
      }

      // 关闭添加弹窗
      showAddPopup.value = false;
      addFormData.value = {};

      // 刷新选项列表（可选，如果直接添加到列表中可以不刷新）
      if (config.addApi.refreshAfterAdd !== false) {
        await refreshList();
      }

      // 延迟重新打开选择器弹窗
      setTimeout(() => {
        showPopup.value = true;
      }, 100);

    } else {
      throw res.msg || '添加失败';
    }
  } catch (error) {
    console.error('添加失败:', error);
    showToast(error.toString());

    // 执行错误回调
    if (config.addApi.errorCallback && typeof config.addApi.errorCallback === 'function') {
      config.addApi.errorCallback(error);
    }
  } finally {
    addFormLoading.value = false;
  }
};
var handleLinkKey = () => {
  if (
    Array.isArray(props.form[props.config.key]) &&
    !props.form[props.config.key].length
  ) {
    props.form[props.config.key] = undefined;
  }
  if (props.config.LinkKeyConfig) {
    props.config.LinkKeyConfig.forEach((cfg) => {
      props.form[cfg.key] = null;
      let chd = props.config.child.form.find((child) => child.key == cfg.key);
      if (chd) {
        let keymap = Object.entries(cfg.postData);
        keymap.forEach((kv) => {
          let value = props.form[kv[1]]
          // chd.opts.postData[kv[0]] = typeof value == "object" ? JSON.stringify(value) : value;
          chd.opts.postData[kv[0]] = value
        });
        // console.log("--子配置--", chd);
      }
    });
    data.copyVal = props.form[props.config.key];
    props.form[props.config.key] = null;
    setTimeout(() => {
      props.form[props.config.key] = data.copyVal;
      console.log("--init-----", data.copyVal, props.form[props.config.key]);
    }, 20);
  }
};
var setText = (list) => {
  list = list || selectLs;
  // console.log("=====setText=====",list);
  if (list.length) {
    if (list.length < 3) {
      data.value = list.map((el) => el[config.opts.text_key]).toString();
    } else {
      data.value = `${list
        .slice(0, 2)
        .map((el) => el[config.opts.text_key])
        .toString()}..`;
    }
  } else {
    data.value = "";
  }
};
var handleShowPopup = (v) => {
  // console.log("=====handleShowPopup=====",v);

  let contrast_key = config.opts.contrast_key;
  let handleLs = handleFormData(
    config,
    v ? props.form[config.key] : selectLs,
    !v,
    props.form
  );
  if (v) {
    if (!selectLs.length) {
      list.value.forEach((el) => {
        if (handleLs && handleLs.find) {
          handleLs.find((ks) => {
            if (el[contrast_key] == ks[contrast_key]) {
              el.select = true;
              return true;
            } else {
              el.select = false;
            }
          });
        } else {
          if (el[contrast_key] == handleLs) {
            el.select = true;
            return true;
          } else {
            el.select = false;
          }
        }
      });
      selectLs = list.value.filter((el) => el.select);
    } else {
      // console.log("确认未选择------》", handleLs);
    }
    setText(
      handleLs && handleLs.length && !selectLs.length ? handleLs : selectLs
    );
  } else if (!v) {
    if (isListItemClick) {
      console.log(`[数据保存] ${config.label} 保存数据:`, {
        key: config.key,
        handleLs,
        selectLs,
        keyMap: config.opts.keyMap
      });
      props.form[config.key] = handleLs;
      setText(selectLs);
    }
  }
  isListItemClick = false;
  nextTick(proxy.$forceUpdate);
};
var onClick = () => {
  if (!config.disabled) {
    // TODO: 检查是否需要重新加载联动数据
    if (config.scheduledTaskLinkage.enabled && config.scheduledTaskLinkage.parentKey) {
      const parentValue = props.form[config.scheduledTaskLinkage.parentKey];

      console.log(`[计划任务联动] ${config.label} 点击事件检查:`, {
        parentKey: config.scheduledTaskLinkage.parentKey,
        parentValue,
        listLength: list.value.length,
        hasUrl: !!config.opts.url
      });

      if (parentValue && config.opts.url) {
        // 父级字段有值且有接口URL，检查是否需要加载数据
        if (list.value.length === 0) {
          console.log(`[计划任务联动] ${config.label} 列表为空，重新加载数据`);
          // 更新请求参数并加载数据
          handleScheduledTaskLinkage();
          onLoad();
          return; // onLoad中会调用handleShowPopup(true)
        }
      } else if (!parentValue) {
        console.log(`[计划任务联动] ${config.label} 父级字段无值，显示空列表`);
        // 父级字段没有值，显示空列表
        list.value = [];
      }
    }

    showPopup.value = true;
  }
};
var onListItemClick = (item) => {
  isListItemClick = true;
  item.select = !item.select;
  if (!config.opts.multiple) {
    // 单选模式：清除其他项的选中状态
    list.value.forEach((el) => {
      if (el != item) {
        el.select = false;
      }
    });
    // 单选模式：点击后直接关闭弹窗并确认选择
    showPopup.value = false;
    selectLs = list.value.filter((el) => el.select);
    setTimeout(() => {
      emit("onConfirm", {
        data: props.form[config.key],
      });
      if (!selectLs.length) {
        props.form[config.key] = [];
        setText([]);
      }
    });
    if (props.config.LinkKeyConfig) {
      setTimeout(handleLinkKey, 20);
    }
  }
  triggerRef(list);
  // proxy.$forceUpdate()
};

var handleData = (data) => {
  var keyMap = Object.entries(config.card);
  data.forEach((element) => {
    keyMap.forEach((keyLs) => {
      element[keyLs[0]] = element[keyLs[1]];
    });
  });
};

var onLoad = () => {
  data.loading = true;
  let query = { ...config.opts.postData };
  proxy
    .$get(config.opts.url, query)
    .then((res) => {
      if (res.code === 200) {
        res = res.data;
        if (res.length) {
          handleData(res);
          list.value = res;
          handleShowPopup(true);
        } else {
          list.value = []
          handleShowPopup(true)
          data.finished = true;
        }
      } else {
        data.error = true;
      }
    })
    .catch((err) => {
      data.error = true;
      console.log(err);
      showToast(res.msg);
    })
    .finally(() => {
      data.loading = false;
    });
};
var handleLinkKeyPostData = () => {
  if (props.config.parentKeyConfig) {
    config.opts.postData[props.config.parentKeyConfig.postKey] = props.form[props.config.parentKeyConfig.key]
  }
};

// TODO: 处理计划任务联动逻辑
var handleScheduledTaskLinkage = () => {
  // 只有启用联动功能时才处理
  if (!config.scheduledTaskLinkage.enabled) {
    return;
  }

  const { parentKey, parentPostKey } = config.scheduledTaskLinkage;

  // 检查父级字段是否有值
  if (parentKey && props.form[parentKey]) {
    // 提取父级字段的值
    let parentValue = props.form[parentKey];

    console.log(`[计划任务联动] ${config.label} 原始父级数据:`, {
      parentKey,
      parentPostKey,
      originalValue: parentValue,
      valueType: typeof parentValue,
      isArray: Array.isArray(parentValue),
      keys: typeof parentValue === 'object' ? Object.keys(parentValue) : 'N/A'
    });

    // 如果父级值是数组，提取第一个元素
    if (Array.isArray(parentValue) && parentValue.length > 0) {
      parentValue = parentValue[0];
      console.log(`[计划任务联动] ${config.label} 提取数组第一个元素:`, parentValue);
    }

    // 如果父级值是对象，提取其id或value属性
    if (typeof parentValue === 'object' && parentValue !== null) {
      if (parentValue.id !== undefined) {
        parentValue = parentValue.id;
        console.log(`[计划任务联动] ${config.label} 提取id值:`, parentValue);
      } else if (parentValue.value !== undefined) {
        parentValue = parentValue.value;
        console.log(`[计划任务联动] ${config.label} 提取value值:`, parentValue);
      } else {
        console.log(`[计划任务联动] ${config.label} 对象没有id或value属性:`, Object.keys(parentValue));
      }
    }

    // 更新当前选择器的请求参数
    if (parentPostKey && parentValue) {
      // 清空之前的postData，重新设置
      config.opts.postData = { [parentPostKey]: parentValue };
      console.log(`[计划任务联动] ${config.label} 最终请求参数:`, {
        [parentPostKey]: parentValue,
        postData: config.opts.postData,
        url: config.opts.url
      });
    }
  } else {
    // 父级字段没有值，清空请求参数
    if (parentPostKey) {
      delete config.opts.postData[parentPostKey];
    }
  }
};
// TODO: 初始化数据加载逻辑
var initializeData = () => {
  if (config.opts.url) {
    handleLinkKeyPostData()
    // TODO: 处理计划任务联动数据
    handleScheduledTaskLinkage()

    // TODO: 检查是否需要等待父级字段选择
    if (config.scheduledTaskLinkage.enabled && config.scheduledTaskLinkage.parentKey) {
      // 如果启用了联动且有父级字段，检查父级字段是否有值
      const parentValue = props.form[config.scheduledTaskLinkage.parentKey];
      if (!parentValue) {
        // 父级字段没有值，不加载数据，显示提示
        list.value = [];
        handleShowPopup(true);
        return;
      }
    }

    onLoad();
  } else {
    list.value = JSON.parse(JSON.stringify(config.opts.defaultList));
    handleShowPopup(true);
  }
};

// 执行初始化
initializeData();
watch(showPopup, handleShowPopup);

// TODO: 监听父级字段变化，实现计划任务联动
watch(() => {
  if (config.scheduledTaskLinkage.enabled && config.scheduledTaskLinkage.parentKey) {
    return props.form[config.scheduledTaskLinkage.parentKey];
  }
  return null;
}, (newValue, oldValue) => {
  // 只有启用联动且父级字段发生变化时才处理
  if (config.scheduledTaskLinkage.enabled && newValue !== oldValue) {
    console.log(`[计划任务联动] ${config.label} 检测到父级字段变化:`, {
      parentKey: config.scheduledTaskLinkage.parentKey,
      oldValue,
      newValue
    });

    // 清空当前字段的值和显示文本
    props.form[config.key] = '';
    data.value = '';

    // 如果父级字段有值，更新请求参数并重新加载数据
    if (config.opts.url && newValue) {
      console.log(`[计划任务联动] ${config.label} 父级字段有值，重新加载数据`);
      handleScheduledTaskLinkage();
      onLoad();
    } else {
      // 父级字段没有值，清空列表和当前选中值
      console.log(`[计划任务联动] ${config.label} 父级字段无值，清空所有数据`);
      list.value = [];
      props.form[config.key] = config.opts.multiple ? [] : '';
      data.value = '';
    }
  }
}, { deep: true });

var update = () => { };
</script>
<style lang="scss">
.wrapper {
  &.required-field {
    .van-field {
      --van-field-label-margin-right: 20px;

      .van-field__label {
        margin-left: -8px !important;
      }
    }


  }

  .van-field {
    font-size: 16px;
    // line-height: 22px;
    padding: 16px;
  }

  .top-block {
    padding: 16px 32px;
    display: flex;
    justify-content: space-between;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
  }

  .but-block {
    margin-top: 50px;
    padding: 0 32px;
    text-align: right;
    //  display: flex;
    //   justify-content: space-between;
  }

  .list-content {
    height: calc(100% - 50px);
    margin-top: 50px;
    overflow: scroll;

    .list-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid #f5f5f5;
      cursor: pointer;

      &:last-child {
        border-bottom: none;
      }

      // 多选模式样式
      &.multiple-mode {
        .checkbox-icon {
          margin-right: 12px;

          .checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #ddd;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &.checked {
              background-color: #007fff;
              border-color: #007fff;
            }
          }
        }

        .item-content {
          flex: 1;

          .item-title {
            font-size: 16px;
            color: #333;
            line-height: 1.4;
          }
        }
      }

      // 单选模式样式
      &.single-mode {
        justify-content: space-between;

        .item-content {
          flex: 1;

          .item-title {
            font-size: 16px;
            color: #333;
            line-height: 1.4;
          }
        }

        .check-icon {
          margin-left: 12px;
          width: 20px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }

    // 保留原有的卡片样式（如果需要）
    .van-card {
      flex: 1;
      margin-right: 16px;
      border-radius: 4px;

      .van-card__title {
        font-size: 14px;
      }

      .van-card__content {
        min-height: 0 !important;
      }
    }

    .desc {
      color: rgba(0, 0, 0, 0.6);
      font-size: 13px;
      margin: 3px 0;
    }

    // 添加按钮样式
    .add-button-container {
      // padding: 10px;
      padding-bottom: 0;

      .add-button {
        display: flex;
        align-items: center;
        padding: 16px;
        background: #fff;
        border-radius: 8px;

        .add-icon {
          width: 24px;
          height: 24px;
          margin-right: 8px;
          color: #1989FA;
        }

        span {
          font-size: 16px;
          font-weight: normal;
          line-height: 22px;
          color: #1989FA;
          // color: #323233;
        }
      }
    }
  }

  // 添加表单弹窗样式（居中显示）
  .add-form-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;

    .add-form-header {
      padding: 16px 20px;
      text-align: center;
      border-bottom: 1px solid #f5f5f5;
      background-color: #fff;
      flex-shrink: 0;

      span {
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
    }

    .add-form-footer {
      padding: 16px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-top: 1px solid #f5f5f5;
      background-color: #fff;
      flex-shrink: 0;

      span {
        font-size: 16px;
        font-weight: 500;
        color: #007fff;
        cursor: pointer;
        min-width: 60px;
        text-align: center;

        &.disabled {
          color: #c8c9cc !important;
          pointer-events: none;
        }
      }
    }

    .add-form-content {
      flex: 1;
      overflow-y: auto;
      padding: 0;
      background-color: #fff;
      min-height: 200px;
      max-height: 400px;

      .van-form {
        padding: 16px 0;
      }

      .van-field {
        background-color: #fff;
        padding: 12px 20px;

        &:not(:last-child) {
          border-bottom: 1px solid #f5f5f5;
        }

        .van-field__label {
          color: #333;
          font-weight: 500;
        }

        .van-field__control {
          color: #666;
        }
      }
    }
  }
}
</style>
