<template>
    <div class="home-container">
        <!-- yhc-form 组件展示 -->
        <yhc-form :config="basicFormConfig" pageType="detail" :editRedirectConfig="editRedirectConfig"
            @onSubmit="onBasicSubmit" />
    </div>
</template>

<script setup>
import { showToast } from 'vant'
import { useRoute } from 'vue-router'

const route = useRoute()

// 基础表单配置
const basicFormConfig = {
    button: {
        isShow: true,
    },
    postData: {
        id: route.query.id // 从路由获取id参数
    },
    curl: {
        info: '/attendance_rule/get_info', // 获取详情接口
        del: '/attendance_rule/post_del' // 删除接口
    },
    groupForm: [
        [0, 1],
        [1, 2],
    ],
    form: [
        {
            label: "考勤规则",
            key: "rule_name",
            component: "yhc-input",
            type: "text",
            placeholder: "请输入",
            required: true,
            disabled: true,
            rules: [{ required: true, message: "请填写考勤规则名称" }],
        },
        {
            label: "查询方式",
            key: "query_type",
            component: "yhc-radio-group",
            options: [
                { value: "0", label: "按时间" },
                { value: "1", label: "按班次" }
            ],
            disabled: true,
            shape: "dot",
            required: true,
            child: {
                map: {
                    "0": [
                        {
                            label: "开始时间",
                            type: "time",
                            // format: 'HH:mm:ss',
                            key: "start_time",
                            component: "yhc-picker-date",
                            required: true,
                            disabled: true,
                            rules: [{ required: true, message: "请填写开始时间" }],
                        },
                        {
                            label: "结束时间",
                            type: "time",
                            // format: 'HH:mm:ss',
                            key: "end_time",
                            component: "yhc-picker-date",
                            required: true,
                            disabled: true,
                            rules: [{ required: true, message: "请填写结束时间" }],
                        },
                        {
                            label: "查询前置",
                            type: "number",
                            key: "pre_days",
                            "right-icon": "/天",
                            component: "yhc-input",
                            required: true,
                            default: 0,
                            disabled: true,
                            rules: [{ required: true, message: "请填写前置日期" }],
                        },
                    ],
                    "1": [{
                        label: "班次",
                        key: "shifts",
                        component: "yhc-picker",
                        required: true,
                        disabled: true,
                        rules: [{ required: true, message: "请选择班次" }],
                        opts: {
                            url: "",
                            postData: {},
                            merge: false,
                            multiple: true,
                            text_key: "name",
                            contrast_key: "id",
                            keyMap: {},
                            defaultList: [],
                        },
                        card: {
                            // num: "id",
                            // price: "2.00",
                            // desc: "desc",
                            title: "name",
                            // thumb: "https://fastly.jsdelivr.net/npm/@vant/assets/ipad.jpeg",
                        }
                    },
                    {
                        // 默认为8小时，以当前时间为基准向前回溯指定小时数
                        label: "查询时长",
                        key: "query_hours",
                        type: "number",
                        default: 8,
                        disabled: true,
                        required: true,
                        // placeholder:"请填写查询时长/h",
                        "right-icon": "/h",
                        component: "yhc-input",
                        rules: [{ required: false, message: "请填写查询时长/h" }],
                    },
                ],
                },
                form: [],
            },
        },
    ]
}

// 修改按钮跳转配置
const editRedirectConfig = {
    path: '/systemConfig/attendanceRuleAdd', // 跳转到新增页面进行编辑
    query: {
        id: route.query.id, // 传递id参数
        from: 'detail' // 标识来源
    }
}
const { proxy } = getCurrentInstance();
const setRight = () => {
    proxy.$_dd.biz.navigation.setTitle({
        title: '规则详情',
    });
};
setRight()
// 表单提交处理函数
const onBasicSubmit = (data) => {
    console.log('基础表单提交:', data)
    showToast('基础表单提交成功')
}
</script>

<style lang="scss" scoped>
.home-container {
    padding: 0;
    min-height: 100vh;
    // background: #f7f8fa;
}
</style>
