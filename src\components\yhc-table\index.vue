<template>
  <div class="yhc-table-container">
    <!-- 横屏按钮 -->
    <div v-if="config.landscape.enabled" class="landscape-button-container">
      <div class="landscape-info" v-if="data.isLandscape">
        <span>{{ getLandscapeTip() }}</span>
      </div>
      <van-button :type="data.isLandscape ? 'warning' : 'primary'" size="small"
        :icon="data.isLandscape ? 'shrink' : 'enlarge'" @click="toggleLandscape" :loading="data.landscapeLoading"
        class="landscape-toggle-btn">
        {{ data.isLandscape ? '退出横屏' : '横屏展示' }}
      </van-button>
    </div>

    <!-- 表格滚动容器 -->
    <div class="table-scroll-container" :class="{ 'landscape-mode': data.isLandscape }">
      <!-- 表格头部 -->
      <div class="table-header" v-if="config.showHeader">
        <div class="header-row">
          <div class="header-cell" v-for="(column, index) in config.columns" :key="index"
            :style="{ width: column.width || 'auto', minWidth: column.minWidth || '80px' }">
            {{ column.title }}
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="table-body">
        <!-- 骨架屏 -->
        <div v-if="data.showSkeleton && config.skeleton.isShow" class="skeleton-container">
          <van-skeleton v-for="n in config.skeleton.count" :key="n" :row="config.skeleton.row"
            :row-width="config.skeleton.rowWidth" :loading="true" :avatar="false" :title="false"
            class="skeleton-item" />
        </div>

        <!-- 表格数据行 -->
        <div v-else>
          <div class="table-row" v-for="(item, rowIndex) in data.list" :key="rowIndex"
            @click="onRowClick(item, rowIndex)">
            <div class="table-cell" v-for="(column, colIndex) in config.columns" :key="colIndex" :style="{
              width: column.width || 'auto',
              minWidth: column.minWidth || '80px',
              textAlign: column.align || 'left'
            }" :class="{ 'no-wrap': !config.wordWrap }">
              <!-- 自定义插槽 -->
              <div v-if="column.slot" class="cell-content">
                <slot :name="column.slot" :item="item" :value="getColumnValue(item, column)" :rowIndex="rowIndex"
                  :column="column"></slot>
              </div>
              <!-- 默认显示 -->
              <div v-else class="cell-content">{{ formatValue(getColumnValue(item, column), column) }}</div>
            </div>
          </div>

          <!-- 空数据提示 -->
          <div v-if="data.list.length === 0 && !data.loading" class="empty-data">
            <van-empty image-size="80" description="暂无数据" />
          </div>


        </div>
      </div>

      <!-- 加载更多 - 始终显示，无需条件 -->
      <div class="table-footer">
        <!-- 创建一个和表格行相同结构的容器来确保宽度一致 -->
        <div class="footer-row">
          <div class="footer-cell" v-for="(column, index) in config.columns" :key="index" :style="{
            width: column.width || 'auto',
            minWidth: column.minWidth || '80px'
          }">
            <!-- 只在第一个cell中显示van-list，其他cell为空 -->
            <van-list v-if="index === 0" v-model:loading="data.loading" v-model:error="data.error"
              error-text="请求失败，点击重新加载" :finished="data.finished" finished-text="没有更多了" @load="onLoad">
              <!-- van-list需要有内容才能正确工作 -->
              <div></div>
            </van-list>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, getCurrentInstance, onBeforeUnmount, nextTick } from 'vue'
import { deepAssign } from '@/untils'
import { showToast } from 'vant'

const { proxy } = getCurrentInstance()

const props = defineProps({
  config: {
    type: Object,
    required: true
  }
})

// 默认配置
const config = reactive({
  // 表格列配置 - 定义表格的列结构
  columns: [
    // 列配置示例:
    // {
    //   key: 'name',           // 数据字段名 (字符串/函数) - 对应数据中的字段名，或自定义取值函数
    //   title: '姓名',         // 表头显示文字 (字符串) - 列标题显示的文本
    //   width: '100px',        // 列宽 (字符串) - 固定列宽，如'100px', '20%'
    //   minWidth: '80px',      // 最小宽度 (字符串) - 列的最小宽度，防止内容被压缩
    //   align: 'left',         // 对齐方式 (字符串) - 'left': 左对齐, 'center': 居中, 'right': 右对齐
    //   slot: 'name',          // 自定义插槽名 (字符串) - 使用插槽自定义列内容渲染
    //   formatter: null        // 格式化函数 (函数) - 自定义数据格式化，如日期格式化、数字格式化
    // }
  ],

  // 表格显示设置
  showHeader: true,          // 是否显示表头 (布尔值) - true: 显示列标题, false: 隐藏表头
  stripe: true,              // 是否显示斑马纹 (布尔值) - true: 奇偶行不同背景色, false: 统一背景
  border: true,              // 是否显示边框 (布尔值) - true: 显示表格边框, false: 无边框样式
  wordWrap: true,            // 是否启用文字换行 (布尔值) - true: 文字自动换行, false: 文字超出截断

  // 横屏展示配置 - 移动端横屏查看功能
  landscape: {
    enabled: true,           // 是否启用横屏功能 (布尔值) - true: 显示横屏按钮, false: 禁用横屏
    autoRotate: true         // 是否自动旋转屏幕 (布尔值) - true: 自动旋转设备屏幕, false: 仅UI横屏
  },

  // 数据接口配置
  curl: {
    ls: '',                  // 列表数据接口地址 (字符串) - 例: "/api/table/list" - 获取表格数据的API
  },
  postData: {},              // 请求参数 (对象) - 发送给接口的参数，如: {page: 1, size: 20, filter: 'active'}
  mockData: false,           // 是否使用模拟数据 (布尔值) - true: 使用本地模拟数据, false: 调用真实API

  // 分页配置 - 控制数据分页加载
  pagination: {
    enabled: true,           // 是否启用分页 (布尔值) - 现在无条件启用，此配置保留兼容性
    pageSize: 20             // 每页数量 (数字) - 每次加载的数据条数，建议10-50之间
  },

  // 骨架屏配置 - 数据加载时的占位效果
  skeleton: {
    isShow: false,           // 是否启用骨架屏 (布尔值) - true: 显示加载占位效果, false: 不显示
    count: 5,                // 骨架屏数量 (数字) - 显示多少行骨架屏占位，建议3-8行
    row: 1,                  // 每个骨架屏的行数 (数字) - 每行占位的高度行数，表格通常为1
    rowWidth: ['100%'],      // 每行的宽度 (数组) - 占位条的宽度，表格通常为['100%']
    duration: 2000           // 骨架屏显示时长 (数字) - 毫秒，超过此时间自动隐藏，0表示不自动隐藏
  }
})

// 组件数据
const data = reactive({
  list: [],                   // 表格数据
  loading: false,             // 加载状态
  finished: false,            // 是否加载完成
  error: false,               // 错误状态
  showSkeleton: false,        // 骨架屏显示状态
  isLandscape: false,         // 是否横屏模式
  landscapeLoading: false,    // 横屏切换加载状态
  page: {
    page: 1,
    per_page: 20
  }
})

// 初始化配置
props.config && deepAssign(config, props.config)

// 监听配置变化
watch(() => props.config, (newConfig) => {
  if (newConfig) {
    deepAssign(config, newConfig)
    console.log('yhc-table配置已更新:', config)
  }
}, { deep: true, immediate: true })

// 事件定义
const emits = defineEmits(['rowClick', 'load'])

// 获取列值
const getColumnValue = (item, column) => {
  if (typeof column.key === 'function') {
    return column.key(item)
  }
  return item[column.key]
}

// 格式化值
const formatValue = (value, column) => {
  if (column.formatter && typeof column.formatter === 'function') {
    return column.formatter(value)
  }
  return value
}

// 行点击事件
const onRowClick = (item, index) => {
  emits('rowClick', item, index)
}

// 显示骨架屏
const showSkeletonScreen = () => {
  data.showSkeleton = true
  setTimeout(() => {
    data.showSkeleton = false
  }, config.skeleton.duration)
}

// 初始化数据
const initData = () => {
  data.list = []
  data.page.page = 1
  data.finished = false
  data.error = false
  data.showSkeleton = false
}

// 获取横屏提示文字 - 移除所有提示文字显示
const getLandscapeTip = () => {
  // 返回空字符串，不显示任何提示文字
  return ''
}

// 加载数据
const onLoad = () => {
  console.log(`🚀 onLoad被调用，当前状态: loading=${data.loading}, finished=${data.finished}, page=${data.page.page}`)

  // 如果已经加载完成，直接返回
  if (data.finished) {
    console.log(`✋ 数据已加载完成，finished=${data.finished}`)
    data.loading = false // 确保loading状态为false
    return
  }

  // 如果是首次加载且启用了骨架屏，显示骨架屏
  if (data.page.page === 1 && config.skeleton.isShow && data.list.length === 0) {
    showSkeletonScreen()
  }

  console.log(`⏳ 开始加载第${data.page.page}页数据...（5秒后显示结果）`)
  let query = { ...data.page, ...config.postData }
  const currentPage = data.page.page
  data.page.page++

  // 有真实API接口
  if (config.curl.ls && !config.mockData) {
    proxy.$post(config.curl.ls, query)
      .then((res) => {
        if (!res.errcode) {
          res = res.result
          if (res.data && res.data.length > 0) {
            data.list.push(...res.data)
          } else {
            data.finished = true
          }
        } else {
          data.error = true
          throw res.errmsg
        }
      })
      .catch((err) => {
        data.error = true
        showToast(err)
      })
      .finally(() => {
        data.loading = false
        data.showSkeleton = false
      })
  } else {
    // 模拟数据或没有API接口时，使用模拟数据 - 1秒延迟演示效果
    const mockDelay = 1000 // 1秒延迟，方便观察加载效果
    setTimeout(() => {
      const mockData = generateMockData(currentPage)
      if (mockData && mockData.length > 0) {
        data.list.push(...mockData)
        console.log(`✅ 已加载第${currentPage}页数据，共${mockData.length}条，总数据量：${data.list.length}`)
      } else {
        data.finished = true
        console.log('🎉 模拟数据加载完成，没有更多数据')
      }
      data.loading = false
      data.showSkeleton = false
      console.log(`📊 加载完成，当前状态: loading=${data.loading}, finished=${data.finished}, 总数据：${data.list.length}条`)

      // 使用nextTick确保状态更新被正确应用
      nextTick(() => {
        console.log(`🔄 nextTick后状态: loading=${data.loading}, finished=${data.finished}`)
        // 强制确保loading状态为false，让van-list可以继续工作
        if (data.loading) {
          console.log('⚠️ 发现loading仍为true，强制设置为false')
          data.loading = false
        }
        if (currentPage === 1) {
          console.log('🎯 第一页加载完成，van-list已准备好下次加载')
        }
      })
    }, mockDelay)
  }
}

// 生成模拟数据 - 根据页码返回不同数据
const generateMockData = (page = 1) => {
  // 模拟总共3页数据，每页不同内容
  const allMockData = {
    1: [
      { name: '实收款', type: '学生', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '1844.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '第一档', type: '合营制', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '100.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '冷荤菜', type: '一科技内部开发平台', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '100.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '来宾菜', type: '一科技内部开发平台', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '100.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '菜品', type: '一科技内部开发平台', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '100.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' }
    ],
    2: [
      { name: '零售菜', type: '一科技内部开发平台', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '20.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '会员菜', type: '学生', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '100.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '套餐A', type: '标准套餐', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '25.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '套餐B', type: '豪华套餐', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '35.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' }
    ],
    3: [
      { name: '饮料', type: '饮品类', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '5.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '水果', type: '水果类', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '8.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' },
      { name: '小食', type: '零食类', count: 0, amount1: '0.00', amount2: '0.00', count2: 0, count3: 0, amount3: '0.00', amount4: '3.00', amount5: '0.00', amount6: '0.00', amount7: '0.00', amount8: '0.00' }
    ]
  }

  // 返回对应页的数据，如果页数超出范围则返回空数组
  return allMockData[page] || []
}

// 检测设备类型和浏览器
const getDeviceInfo = () => {
  const ua = navigator.userAgent
  const isIOS = /iPad|iPhone|iPod/.test(ua)
  const isAndroid = /Android/.test(ua)
  const isMobile = /Mobi|Android/i.test(ua)
  const isWechat = /MicroMessenger/i.test(ua)
  const isChrome = /Chrome/.test(ua) && !/Edge/.test(ua)
  const isSafari = /Safari/.test(ua) && !/Chrome/.test(ua)

  return { isIOS, isAndroid, isMobile, isWechat, isChrome, isSafari }
}

// 检测当前屏幕方向
const checkOrientation = () => {
  if (window.screen && window.screen.orientation) {
    const orientation = window.screen.orientation.type
    return {
      isLandscape: orientation.startsWith('landscape'),
      isPortrait: orientation.startsWith('portrait'),
      type: orientation
    }
  } else {
    // 降级处理
    const isLandscape = window.innerWidth > window.innerHeight
    return {
      isLandscape,
      isPortrait: !isLandscape,
      type: isLandscape ? 'landscape-primary' : 'portrait-primary'
    }
  }
}



// 横屏切换功能 - 根据CSDN文档优化
const toggleLandscape = async () => {
  data.landscapeLoading = true
  const deviceInfo = getDeviceInfo()

  try {
    if (!data.isLandscape) {
      // 进入横屏模式
      data.isLandscape = true
      document.body.classList.add('table-landscape-mode')

      // 方法1: 尝试使用 Screen Orientation API (推荐)
      if (config.landscape.autoRotate && screen.orientation && screen.orientation.lock) {
        try {
          // 请求全屏显示 + 锁定横屏
          if (document.documentElement.requestFullscreen) {
            await document.documentElement.requestFullscreen()
          }
          await screen.orientation.lock('landscape')
          showToast('已自动切换至横屏模式')
        } catch (error) {
          console.warn('自动横屏失败，使用降级方案:', error)
          handleCSSRotation(deviceInfo)
        }
      } else {
        // 方法2: CSS3旋转 + 提示用户
        handleCSSRotation(deviceInfo)
      }

    } else {
      // 退出横屏模式
      data.isLandscape = false
      document.body.classList.remove('table-landscape-mode')

      // 重置表格容器样式
      resetTableStyles()

      // 解锁屏幕方向并退出全屏
      if (screen.orientation && screen.orientation.unlock) {
        screen.orientation.unlock()
      }
      if (document.exitFullscreen) {
        document.exitFullscreen().catch(() => { })
      }

      // 确保页面恢复正常滚动
      document.body.style.overflow = ''
      document.documentElement.style.overflow = ''

      // 移除横屏模式的body类，恢复正常滚动
      setTimeout(() => {
        document.body.style.removeProperty('overflow')
      }, 100)

      showToast('已退出横屏模式')
    }
  } catch (error) {
    console.warn('横屏切换失败:', error)
    handleCSSRotation(deviceInfo)
  } finally {
    data.landscapeLoading = false
  }
}

// CSS3旋转方案 - 参考文档中的detectOrient方法
const handleCSSRotation = (deviceInfo) => {
  const width = document.documentElement.clientWidth
  const height = document.documentElement.clientHeight
  const $wrapper = document.querySelector('.yhc-table-container')

  if (!$wrapper) return

  let style = ""
  const currentOrientation = checkOrientation()

  if (currentOrientation.isLandscape) {
    // 已经是横屏，直接显示
    style += `width: ${width}px; height: ${height}px;`
    style += "transform: rotate(0deg);"
    style += "transform-origin: 0 0;"
    showToast('横屏模式已激活')
  } else {
    // 竖屏状态，通过CSS旋转实现横屏效果
    style += `width: ${height}px; height: ${width}px;`
    style += "transform: rotate(90deg);"
    style += `transform-origin: ${width / 2}px ${width / 2}px;`

    if (deviceInfo.isIOS) {
      showToast('请手动旋转设备至横屏以获得最佳体验')
    } else if (deviceInfo.isAndroid) {
      showToast('请旋转设备或在设置中开启自动旋转')
    } else {
      showToast('已进入横屏模式，建议旋转设备获得更好体验')
    }
  }

  $wrapper.style.cssText += style
}

// 重置表格样式
const resetTableStyles = () => {
  const $wrapper = document.querySelector('.yhc-table-container')
  if ($wrapper) {
    // 清除所有横屏相关的内联样式
    $wrapper.style.transform = ''
    $wrapper.style.transformOrigin = ''
    $wrapper.style.width = ''
    $wrapper.style.height = ''

    // 强制重置为初始状态
    $wrapper.style.transform = 'none'
    $wrapper.style.transformOrigin = 'initial'
    $wrapper.style.width = 'auto'
    $wrapper.style.height = 'auto'

    // 触发重排，确保样式生效
    $wrapper.offsetHeight

    // 移除可能的横屏相关类名
    $wrapper.classList.remove('landscape-mode')

    // 清除内联样式，恢复CSS控制
    setTimeout(() => {
      $wrapper.style.removeProperty('transform')
      $wrapper.style.removeProperty('transform-origin')
      $wrapper.style.removeProperty('width')
      $wrapper.style.removeProperty('height')

      // 确保表格滚动容器也恢复正常
      const $scrollContainer = $wrapper.querySelector('.table-scroll-container')
      if ($scrollContainer) {
        $scrollContainer.classList.remove('landscape-mode')
      }
    }, 50)
  }
}





// 刷新表格
const refresh = () => {
  console.log('正在刷新表格数据...')
  initData()
  if (config.skeleton.isShow) {
    showSkeletonScreen()
  }
  onLoad()
}

// 暴露方法
defineExpose({ refresh })

// 监听屏幕方向变化 - 根据文档优化
const handleOrientationChange = () => {
  if (!data.isLandscape) return

  setTimeout(() => {
    const orientation = checkOrientation()
    const deviceInfo = getDeviceInfo()

    if (orientation.isLandscape) {
      // 用户已经旋转到横屏
      showToast('🎉 横屏模式已激活，享受更好的表格浏览体验！')
      // 移除CSS旋转，使用原生横屏
      resetTableStyles()
    } else {
      // 用户旋转回竖屏，但仍在横屏模式
      if (deviceInfo.isIOS) {
        showToast('请保持横屏方向以获得最佳体验')
      } else {
        showToast('建议保持横屏方向浏览表格')
      }
      // 重新应用CSS旋转
      handleCSSRotation(deviceInfo)
    }
  }, 300) // 减少延迟，提高响应速度
}

// 添加屏幕方向变化监听 - 根据文档建议使用多种事件
// 1. orientationchange 事件 - 主要监听方式
window.addEventListener('orientationchange', handleOrientationChange)

// 2. resize 事件 - 兼容不支持orientationchange的设备
window.addEventListener('resize', handleOrientationChange)

// 3. Screen Orientation API 监听 - 现代浏览器支持
if (screen.orientation) {
  screen.orientation.addEventListener('change', handleOrientationChange)
}

// 组件卸载时清理监听器
onBeforeUnmount(() => {
  // 移除所有事件监听器
  window.removeEventListener('orientationchange', handleOrientationChange)
  window.removeEventListener('resize', handleOrientationChange)

  // 移除 Screen Orientation API 监听器
  if (screen.orientation) {
    screen.orientation.removeEventListener('change', handleOrientationChange)
  }

  // 确保退出时清理横屏状态
  if (data.isLandscape) {
    document.body.classList.remove('table-landscape-mode')

    // 恢复body的正常滚动状态
    document.body.style.removeProperty('overflow')
    document.body.style.removeProperty('position')
    document.body.style.removeProperty('width')
    document.body.style.removeProperty('height')

    // 解锁屏幕方向
    if (screen.orientation && screen.orientation.unlock) {
      screen.orientation.unlock()
    }

    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen().catch(() => { })
    }

    // 重置表格容器样式
    resetTableStyles()
  }
})



// 初始化加载 - 因为分页现在默认启用，始终执行加载
console.log('yhc-table组件初始化，开始加载数据...')
console.log('分页配置:', config.pagination)
onLoad()
</script>

<style lang="scss" scoped>
.yhc-table-container {
  width: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .landscape-button-container {
    padding: 12px 16px;
    border-bottom: 1px solid #ebedf0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;

    .landscape-info {
      flex: 1;

      .landscape-tip {
        font-size: 12px;
        color: #666;
        padding: 4px 8px;
        background: rgba(25, 137, 250, 0.1);
        border-radius: 12px;
        display: inline-block;
      }
    }

    .landscape-toggle-btn {
      margin-left: 12px;
    }
  }

  .table-scroll-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;

    &.landscape-mode {
      // 移除高度限制，避免与全局横屏模式冲突
      overflow-y: auto; // 确保可以垂直滚动
      font-size: 12px;

      .table-cell {
        padding: 8px 6px;
        font-size: 12px;
      }

      .header-cell {
        padding: 10px 6px;
        font-size: 12px;
        font-weight: 600;
      }
    }

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c8c9cc;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: #f7f8fa;
    }
  }

  .table-header {
    background: #f7f8fa;
    border-bottom: 1px solid #ebedf0;
    min-width: max-content;

    .header-row {
      display: flex;

      .header-cell {
        padding: 12px 8px;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        border-right: 1px solid #ebedf0;
        text-align: center;
        white-space: nowrap;
        flex-shrink: 0;

        &:last-child {
          border-right: none;
        }
      }
    }
  }

  .table-body {
    min-width: max-content;

    .table-row {
      display: flex;
      border-bottom: 1px solid #ebedf0;
      align-items: stretch;
      min-height: 48px;

      &:hover {
        background: #f7f8fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .table-cell {
        padding: 12px 8px;
        font-size: 14px;
        color: #646566;
        border-right: 1px solid #ebedf0;
        word-break: break-all;
        word-wrap: break-word;
        white-space: normal;
        flex-shrink: 0;
        line-height: 1.4;
        display: flex;
        align-items: center;

        &.no-wrap {
          .cell-content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        &:last-child {
          border-right: none;
        }

        .cell-content {
          width: 100%;
          word-break: break-all;
          word-wrap: break-word;
        }
      }
    }

    .empty-data {
      padding: 40px 20px;
      text-align: center;
    }

    // 注释掉原来的数据结束提示，使用van-list的finished状态替代
    // &:not(:has(.empty-data))::after {
    //   content: '— 已显示全部数据 —';
    //   display: block;
    //   text-align: center;
    //   color: #c8c9cc;
    //   font-size: 12px;
    //   margin-top: 15px;
    //   padding-top: 10px;
    //   border-top: 1px dashed #f0f0f0;
    // }
  }

  .skeleton-container {
    padding: 16px;

    .skeleton-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .table-footer {
    min-width: max-content; // 关键：确保footer宽度跟随表格内容宽度

    .footer-row {
      display: flex; // 使用和表格行相同的flex布局
      background: rgba(248, 249, 250, 0.95);
      border-top: 1px solid #ebedf0;

      .footer-cell {
        text-align: center;
        // border-right: 1px solid #ebedf0;
        flex-shrink: 0; // 防止收缩，和表格cell保持一致

        &:last-child {
          border-right: none;
        }

        // van-list只在第一个cell中显示，需要特殊处理
        .van-list {
          min-height: 50px;
        }
      }
    }
  }
}
</style>

<style lang="scss">
// 全局横屏模式样式
body.table-landscape-mode {
  // 防止body滚动，但允许内部容器滚动
  overflow: hidden;

  .yhc-table-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    border-radius: 0;
    height: 100vh;
    width: 100vw;
    background: #fff;
    overflow: hidden; // 容器本身不滚动

    .landscape-button-container {
      position: absolute; // 改为绝对定位
      top: 0;
      left: 0;
      right: 0;
      height: 60px;
      background: #fff;
      z-index: 10;
      border-bottom: 1px solid #ebedf0;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .table-scroll-container {
      position: absolute; // 改为绝对定位
      top: 60px; // 从按钮容器下方开始
      left: 0;
      right: 0;
      bottom: 0; // 延伸到底部
      overflow-y: auto; // 允许垂直滚动
      overflow-x: auto;
      -webkit-overflow-scrolling: touch; // iOS平滑滚动
      border: 1px solid #ebedf0; // 添加表格边框
      border-radius: 0; // 横屏模式下不需要圆角

      // 确保滚动条可见且可用
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
      }

      // 横屏模式下的表格优化
      .table-header {
        background: #f7f8fa;
        border-bottom: 1px solid #ebedf0;
        min-width: max-content;

        .header-row {
          display: flex;

          .header-cell {
            padding: 10px 8px;
            font-size: 13px;
            font-weight: 500;
            color: #323233;
            border-right: 1px solid #ebedf0;
            text-align: center;
            white-space: nowrap;
            flex-shrink: 0;

            &:last-child {
              border-right: none;
            }
          }
        }
      }

      .table-body {
        min-width: max-content;
        padding-bottom: 0px; // 横屏模式下增加底部边距，确保最后一行数据可见

        .table-row {
          display: flex;
          border-bottom: 1px solid #ebedf0;
          align-items: stretch;
          min-height: 40px;

          &:hover {
            background: #f7f8fa;
          }

          &:last-child {
            border-bottom: none;
          }

          .table-cell {
            padding: 8px 6px;
            font-size: 12px;
            color: #646566;
            border-right: 1px solid #ebedf0;
            white-space: nowrap;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;

            &:last-child {
              border-right: none;
            }

            .cell-content {
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 100%;
            }
          }
        }

        // 空数据状态
        .empty-data {
          padding: 40px 20px;
        }
      }

      // 加载更多区域的样式优化 - 确保分页加载正常工作
      .table-footer {
        min-width: max-content; // 关键：确保footer宽度跟随表格内容宽度

        .footer-row {
          display: flex; // 使用和表格行相同的flex布局
          background: rgba(248, 249, 250, 0.95);
          border-top: 1px solid #ebedf0;

          .footer-cell {
            // padding: 12px 6px; // 横屏模式下减少padding
            text-align: center;
            // border-right: 1px solid #ebedf0;
            flex-shrink: 0; // 防止收缩，和表格cell保持一致
            font-size: 12px; // 横屏模式下字体稍小

            &:last-child {
              border-right: none;
            }

            // van-list只在第一个cell中显示
            .van-list {
              min-height: 50px;
            }
          }
        }
      }
    }


  }
}

// iOS 横屏提示动画
body.ios-landscape-hint {
  &::before {
    content: '📱 请旋转设备至横屏模式';
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    font-size: 16px;
    animation: fadeInOut 3s ease-in-out;
  }
}

@keyframes fadeInOut {

  0%,
  100% {
    opacity: 0;
  }

  20%,
  80% {
    opacity: 1;
  }
}

// 横屏媒体查询优化
@media screen and (orientation: landscape) {
  body.table-landscape-mode {
    .yhc-table-container {
      .table-scroll-container {

        .table-cell,
        .header-cell {
          font-size: 12px;
          padding: 6px 8px;
        }
      }

      .table-body {
        padding-bottom: 100px; // 横屏模式下增加更多底部边距

        // 横屏模式数据结束提示优化
        &:not(:has(.empty-data))::after {
          content: '— 数据展示完毕，可退出横屏模式 —';
          color: #666;
          font-size: 13px;
          margin-top: 25px;
          padding: 15px 0;
        }
      }
    }
  }
}

// 移动设备横屏优化
@media screen and (max-height: 500px) and (orientation: landscape) {
  body.table-landscape-mode {
    .yhc-table-container {
      .landscape-button-container {
        padding: 8px 16px;
        height: 50px; // 减少按钮容器高度
      }

      .table-scroll-container {
        top: 50px; // 对应减少的按钮高度

        .table-body {
          padding-bottom: 80px; // 确保底部有足够边距
        }

        .table-cell,
        .header-cell {
          padding: 4px 6px;
          font-size: 11px;
        }
      }
    }
  }
}
</style>
