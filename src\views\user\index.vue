<template>
  <div class="wrapper-user" v-if="isShow">
    <div class="user-info ">
    <div class="flex">
      <div class="left flex">
        <van-image round width="48" height="48" :src="user_info.avatar" />
        <div style="margin-left: 8px">
          <div class="flex" style="font-size: 17px;line-height: 22px;color: #FFFFFF;">
            {{ user_info.name }}
            <!-- <van-tag
              style="margin-left: 12px"
              round
              size="medium"
              type="primary"
              color="rgba(0, 127, 255, 0.1)"
              text-color="rgba(0, 127, 255, 1)"
              >用户端</van-tag> -->
          </div>
          <div
            class=""
            style="
              font-size: 12px;
              color: #FFFFFF;
              margin-top: 8px;
              line-height: 18px;
            "
          >
            {{ user_info.dept_name }}
          </div>
        </div>
      </div>
      <div class="right flex" @click="router.push('/payment')">
        <van-icon size="26" name="qr" color="#FFFFFF" />
      </div>
      <!-- <van-button type="primary" round size="small">切换身份</van-button> -->
    </div>
    </div>
    <div class="account">
      <div class="title flex" style="width: 80px">
       <div>
            <div class="text">总资产(元)</div>
            <div class="num">{{money}}</div>
        </div>
        <div>
          <div class="btn">提现</div>
        </div>
        <!--<van-icon
          @click="isShowMoney = !isShowMoney"
          size="20px"
          :name="isShowMoney ? 'eye-o' : 'closed-eye'"
        /> -->
      </div>
      <div class="money-block flex">
        <div>
          <div class="money">
            {{ isShowMoney ? (money*1).toFixed(2) : "****" }}
          </div>
          <div>总资产</div>
        </div>
        <div>
          <div class="money">
            {{ isShowMoney ?( account_info.subsidy_balance*1).toFixed(2) : "****" }}
          </div>
          <div>补贴账户</div>
        </div>
        <div>
          <div class="money" style="color: rgba(22, 120, 255, 100)">
            {{
              isShowMoney ? (account_info.savings_balance * 1).toFixed(2) : "****"
            }}
          </div>
          <div>储蓄账户</div>
        </div>
        <div>
          <div class="money" style="color: rgba(22, 120, 255, 100)">
            {{
              isShowMoney ? (account_info.debt_amount * 1).toFixed(2) : "****"
            }}
          </div>
          <div>待还金额</div>
        </div>
      </div>
      <div class="flex">
        <div class="item" @click="rechargeClick">
          <van-icon size="20" name="peer-pay" />
          <div style="margin-top: 4px">充值</div>
        </div>
        <div class="line"></div>
        <div :class="`item ${!config.balance_refund&&'noclick'}`" @click="moneybutton()">
          <van-icon size="20" name="cash-back-record-o" />
          <div style="margin-top: 4px">提现</div>
        </div>
      </div>
    </div>
    <van-cell-group
      inset
      v-for="(item, i) in list"
      :key="i"
      style="margin-top: 12px"
    >
      <van-cell
        :title="it.title"
        :icon="`/images/common/${it.icon}.png`"
        is-link
        v-for="(it, i) in item"
        :key="it.title"
        @click="onClick(it)"
      >
      </van-cell>
    </van-cell-group>
    <div style="padding-top: 12px">
      <!-- <van-button type="primary" round size="small">切换身份</van-button> -->
    </div>
  </div>
</template>
<script setup>
import { ref, getCurrentInstance, onMounted } from "vue";
import { useRouter } from "vue-router";
import {
  showToast,
  showDialog,
  showLoadingToast,
  showFailToast,
  showSuccessToast,
} from "vant";
import { useLoginStore } from "@/store/dingLogin";


const { proxy } = getCurrentInstance();
const router = useRouter();

const money=ref(0);
// 静态用户信息数据
const user_info = ref({
  name: "张三",
  avatar: "https://img.yzcdn.cn/vant/cat.jpeg",
  department_name: "技术部"
});

// 静态账户信息数据
let account_info = ref({
  account_id: "ACC001",
  account_name: "张三",
  account_type: "个人账户",
  money: 1580.50,        // 总金额
  balance: 800.30,       // 虚拟余额
  real_blance: 780.20,   // 账户余额
});

let isShowMoney = ref(true);
const app = useLoginStore();

let list = [
  [
    {
      title: "转账",
      icon: "zhuanzhang",
      url: "/transfer",
    },
    {
      title: "卡券(2)",
      icon: "youhuijuan",
      url: "/couponlist",
    },
  ],
  [
    {
      title: "产品咨询",
      icon: "chanpin",
      url: "/help",
    },
    {
      title: "帮助",
      icon: "bangzhu",
      url: "/help",
    },
    {
      title: "授权信息",
      icon: "shezhi",
      url: "/authorize",
    },
    {
      title: "需求墙(2)",
      icon: "bangzhu",
      url: "/about",

    }
  ],
];
// if(!sysConfig.bring){
//   list.shift()
// }
const rechargeClick = () => {
  router.push("/recharge");
};
let moneybutton = () => {
  if (!config.value.balance_refund) {
    showToast({position:'top',message:"该功能尚未开启，请联系管理员开启后使用"});
    return;
  }
  showDialog({
    title: "余额退款",
    message: "确认退款，余额将在2个小时内原路返还",
    confirmButtonText: "确认退款",
    showCancelButton: true,
  }).then(() => {
    console.log("提现");
    showLoadingToast({
      message: "退款中...",
      forbidClick: true,
    });

    // {{ AURA-X: Modify - 使用模拟退款逻辑替代真实接口调用 }}
    // 模拟退款成功
    setTimeout(() => {
      showSuccessToast("退款成功");
      // 模拟退款后更新账户余额
      account_info.value.real_blance = 0;
      account_info.value.money = account_info.value.balance * 1 + account_info.value.real_blance * 1;
    }, 2000);

    // 注释掉真实的退款接口调用
    // proxy
    //   .$post(
    //     "account/post_withdraw",
    //     {}
    //   )
    //   .then((res) => {
    //     if (!res.errcode) {
    //       showSuccessToast("退款成功");
    //       getAccount();
    //     }
    //   })
    //   .catch((err) => {
    //     showFailToast("退款失败");
    //   })
    //   .finally(() => {});
  });
};
// 静态配置信息数据
let config = ref({
  balance_refund: true,  // 是否允许余额退款
  bring: true,          // 是否支持代餐功能
  // 其他配置项...
});

let isShow = ref(false);

// 获取账户信息
const getOnfo = () => {
  proxy
    .$get("/user_profile/get_info", {})
    .then((res) => {
      console.log(res);
      user_info.value = res.data;
    })
    .finally(() => {
      isShow.value = true;
    });
};
getOnfo();
//获取账户余额
const getWallet = () => {
  proxy
    .$get("/user_wallet/get_info", {})
    .then((res) => {
      console.log(res);
      account_info.value = res.data;
      money.value = res.data.subsidy_balance+res.data.savings_balance+res.data.debt_amount;
    })
    .finally(() => {
      isShow.value = true;
    });
};
getWallet();
// 使用静态数据初始化
const initStaticData = () => {
  // 模拟接口调用完成，显示页面
  isShow.value = true;
};

// {{ AURA-X: Add - 组件挂载时初始化静态数据 }}
// 组件挂载时初始化静态数据
onMounted(() => {
  initStaticData();
});
let onClick = (item) => {
  if (item.title == "代餐码") {
    // {{ AURA-X: Modify - 使用静态代餐码数据替代真实接口调用 }}
    // 模拟代餐码数据
    const mockMealCode = "MC" + Date.now().toString().slice(-6);

    showDialog({
      title: "代餐码",
      message: `您的代餐码为：${mockMealCode}，注意请勿随意泄露`,
      confirmButtonText: "发送同事",
      showCancelButton: true,
    }).then(() => {
      // 模拟钉钉发送功能（实际环境中需要钉钉SDK支持）
      showToast("代餐码发送功能需要在钉钉环境中使用");

      // 注释掉真实的钉钉API调用
      // proxy.$_dd.biz.ding.create({
      //   users: [],
      //   corpId: app.corpId,
      //   type: 1,
      //   alertType: 2,
      //   alertDate: {
      //     format: "yyyy-MM-dd HH:mm",
      //     value: "",
      //   },
      //   attachment: {
      //     images: [],
      //   },
      //   text: `您的代餐码为：${mockMealCode}，注意请勿随意泄露`,
      //   bizType: 0,
      //   taskInfo: {},
      //   confInfo: {},
      //   onSuccess: function (res) {
      //     // 调用成功时回调
      //     // console.log(res)
      //   },
      //   onFail: function (err) {
      //     // 调用失败时回调
      //     // console.log(err)
      //   },
      // });
    });

    // 注释掉真实的代餐码接口调用
    // proxy.$post("bring/get_info", {}).then((res) => {
    //   showDialog({
    //     title: "代餐码",
    //     message: `您的代餐码为：${
    //       res.result.code || res.result
    //     }，注意请勿随意泄露`,
    //     confirmButtonText: "发送同事",
    //     showCancelButton: true,
    //   }).then(() => {
    //     // ... 钉钉API调用代码
    //   });
    // });
  } else if (item.url == "/help") {
    showToast({
      message: "该功能为Beta版，如有疑问请使用产品咨询联系客服",
      duration: 2000,
      mask: true,
    });
    setTimeout(() => {
      router.push(item.url);
    }, 2000);
  } else if (item.icon == "chanpin") {
    end_time_but_click();
  } else {
    router.push(item.url);
  }
};
const end_time_but_click = () => {
  proxy.$_dd.biz.util.openLink({
    url: `https://page.dingtalk.com/wow/dingtalk/act/serviceconversation?wh_biz=tm&showmenu=false&goodsCode=${app.env.VITE_APP_DT_GOODS}&corpId=${app.corpId}&token=${app.env.VITE_APP_DT_GOODS_TOKEN}`,
    onSuccess: function (result) {
      console.log(result);
    },
    onFail: function (err) {
      console.log(err);
    },
  });
};
const setRight = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: "我的",
    color: "#fff",
    backgroundColor: "#000",

  });
  
};
setRight();

</script>
<style lang="scss">
.wrapper-user {
  width: 100%;
  min-height: 100vh;
  margin-bottom: 40px;
  box-sizing: border-box;
  .van-cell__left-icon {
    display: flex;
    align-items: center;
    .van-icon__image {
      width: 26px;
      height: 26px;
    }
  }
  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .item {
      border-top: 1px solid rgba(0, 0, 0, 0.08);
      flex: 1;
      text-align: center;
      padding: 16px 0;
      font-size: 14px;
      margin-top: 16px;
    }
    .line {
      height: 30px;
      width: 1px;
      background: rgba(0, 0, 0, 0.08);
    }
    .noclick{
      color: rgba(0, 0, 0, 0.3);
    }
  }
  .user-info {
    height: 191px;
    background: linear-gradient(180deg, #007FFF 0%, rgba(0, 119, 255, 0.5905) 68%, rgba(22, 120, 255, 0) 100%);
    padding: 16px;
  }
  .account {

    border-radius: 8px;
    background: #ffffff;
    margin: 16px;
    margin-top:-100px;
    padding: 16px;
    .title {
      font-size: 15px;
      margin-bottom: 8px;
      line-height: 18px;
      color: #323233;
    }
    .text{

    }
    .num{}
    .btn{
      
    }
    .money-block {
      text-align: center;
      padding: 12px 26px 0;
      font-size: 12px;
      color: rgba(23, 26, 29, 0.4);
      .money {
        font-size: 17px;
        color: #171a1d;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
