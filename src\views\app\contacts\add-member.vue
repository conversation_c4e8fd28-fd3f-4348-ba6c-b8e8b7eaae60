<template>
  <div class="add-member-page">
    <!-- 表单内容 -->
    <div class="form-container">
      <div class="group-wrapper">
        <van-field v-model="memberName" label="姓名" :placeholder="namePlaceholder" class="form-field required-field"
          required />
        <van-field v-model="memberPhone" label="手机号" :placeholder="phonePlaceholder" type="tel"
          class="form-field required-field" required />
      </div>
    </div>

    <!-- 底部完成按钮 -->
    <div class="bottom-action">
      <van-button type="primary" size="large" class="complete-btn" @click="handleComplete"
        :disabled="!memberName.trim() || !memberPhone.trim()">
        完成
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast } from 'vant'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const memberName = ref('')
const memberPhone = ref('')

// 计算占位符和类型
const isExternal = computed(() => route.query.type === 'external')
const isEditMode = computed(() => route.query.mode === 'edit')
const namePlaceholder = computed(() => isExternal.value ? '请输入姓名' : '请输入姓名')
const phonePlaceholder = computed(() => '请输入手机号')

// 完成添加
const handleComplete = () => {
  if (!memberName.value.trim()) {
    showToast({
      message: '请输入姓名',
      type: 'fail'
    })
    return
  }

  if (!memberPhone.value.trim()) {
    showToast({
      message: '请输入手机号',
      type: 'fail'
    })
    return
  }

  // 手机号格式验证（现在是必填）
  if (!/^1[3-9]\d{9}$/.test(memberPhone.value.trim())) {
    showToast({
      message: '请输入正确的手机号',
      type: 'fail'
    })
    return
  }

  // 获取部门信息和类型
  const deptName = route.query.deptName || '未知部门'
  const deptId = route.query.deptId

  if (isEditMode.value) {
    // 编辑模式
    console.log('修改人员信息:', {
      id: route.query.id,
      name: memberName.value.trim(),
      phone: memberPhone.value.trim(),
      department: deptName,
      departmentId: deptId
    })
    showSuccessToast('人员信息修改成功')
  } else {
    // 添加模式
    console.log(isExternal.value ? '添加外部人员:' : '添加部门人员:', {
      name: memberName.value.trim(),
      phone: memberPhone.value.trim(),
      department: deptName,
      departmentId: deptId,
      type: isExternal.value ? 'external' : 'department'
    })
    showSuccessToast(isExternal.value ? '外部人员添加成功' : '人员添加成功')
  }

  // 延迟返回上一页
  setTimeout(() => {
    router.back()
  }, 1500)
}

// 加载编辑数据
const loadEditData = () => {
  if (isEditMode.value) {
    // 编辑模式，预填充数据
    memberName.value = route.query.name || ''
    memberPhone.value = route.query.phone || ''
  }
}

// 页面挂载时加载数据
onMounted(() => {
  loadEditData()
})
</script>

<style lang="scss" scoped>
.add-member-page {
  min-height: 100vh;
  background: #f2f3f4;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // 为底部固定按钮留出空间
}

.form-container {
  flex: 1;
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 0px;

  .group-wrapper {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: white;

    .form-field {
      font-size: 16px;
      padding: 16px;

      :deep(.van-field__label) {
        color: #323233;
        font-weight: 500;
      }

      :deep(.van-field__control) {
        color: #323233;

        &::placeholder {
          color: #c8c9cc;
        }
      }

      // 必填字段样式
      &.required-field {
        :deep(.van-field__label) {
          position: relative;

          &::before {
            content: '*';
            color: #ee0a24;
            font-size: 14px;
            margin-right: 4px;
          }
        }
      }
    }
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;

  .complete-btn {
    width: 100%;
    height: 48px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;

    &:disabled {
      background: #c8c9cc;
      border-color: #c8c9cc;
    }
  }
}
</style>
