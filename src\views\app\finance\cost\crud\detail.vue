<template>
  <div class="cost-detail-container">
    <!-- 成本名称 -->
    <van-cell-group inset>
      <van-cell title="成本名称" :value="detailData.name" />
    </van-cell-group>

    <!-- 内部员工和外部人员 -->
    <van-cell-group inset>
      <van-cell title="内部员工" :value="detailData.internalStaff" />
      <van-cell title="外部人员" :value="detailData.externalStaff" />
    </van-cell-group>
  </div>
</template>

<script setup>
import { reactive, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const { proxy } = getCurrentInstance()

// 获取详情数据
const getDetailData = () => {
  const id = route.query.id
  const mockData = {
    1: {
      name: "外勤人员",
      internalStaff: "已选择5个部门；20个成员",
      externalStaff: "已选择5个部门；20个成员"
    },
    2: {
      name: "外勤人员",
      internalStaff: "已选择5个部门；20个成员",
      externalStaff: "已选择6个部门；20个成员"
    }
  }
  return mockData[id] || mockData[1]
}

// 详情数据
const detailData = reactive(getDetailData())

// 设置页面标题
const setPageTitle = () => {
  proxy.$_dd.biz.navigation.setTitle({
    title: '成本详情',
  })
}
setPageTitle()
</script>

<style lang="scss" scoped>
.cost-detail-container {
  min-height: 100%;
  background: #f7f8fa;
  padding-top: 16px;
}

// 自定义van-cell-group样式
:deep(.van-cell-group) {
  margin: 0 16px 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:first-child {
    margin-top: 0;
  }
}

:deep(.van-cell) {
  padding: 16px;

  .van-cell__title {
    font-size: 16px;
    color: #323233;
    font-weight: 400;
    min-width: 80px;
    max-width: 80px;
  }

  .van-cell__value {
    font-size: 16px;
    color: #969799;
    text-align: left;
    padding-left: 0 !important;
    margin-left: 0 !important;
  }

  // 重写van-cell的内部布局
  .van-cell__left {
    margin-right: 15px;
  }

  .van-cell__right {
    text-align: left !important;
  }
}
</style>
