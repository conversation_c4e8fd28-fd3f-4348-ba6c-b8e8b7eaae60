# yhc-table

## 介绍

yhc-table 是一个基于 Vue 3 和 Vant 的移动端表格组件，支持骨架屏、分页加载、自定义列等功能。

## 引入

```js
import yhcTable from '@/components/yhc-table'
```

## 代码演示

### 基础用法

```vue
<template>
  <yhc-table :config="tableConfig" @rowClick="onRowClick">
    <!-- 自定义列插槽 -->
    <template #name="{ item, value }">
      <span style="color: #1989fa;">{{ value }}</span>
    </template>
  </yhc-table>
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px', slot: 'name' },
    { key: 'type', title: '类型', width: '100px' },
    { key: 'amount', title: '金额', width: '80px', align: 'right' }
  ],
  curl: {
    ls: 'api/table-data'
  }
}

const onRowClick = (item, index) => {
  console.log('点击行:', item, index)
}
</script>
```

### 启用骨架屏

```vue
<template>
  <yhc-table :config="tableConfig" />
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px' },
    { key: 'type', title: '类型', width: '100px' },
    { key: 'amount', title: '金额', width: '80px' }
  ],
  skeleton: {
    isShow: true,
    count: 8,
    duration: 2000
  },
  mockData: true // 使用模拟数据
}
</script>
```

### 分页加载演示

```vue
<template>
  <yhc-table :config="tableConfig" />
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px' },
    { key: 'type', title: '类型', width: '100px' },
    { key: 'amount', title: '金额', width: '80px' }
  ]
  // 🎉 无需任何配置！分页加载已无条件启用
  // 📱 组件会自动加载3页模拟数据进行演示
  // ⚡ 每页加载仅需200毫秒，快速响应
}
</script>
```

### 自定义格式化

```vue
<template>
  <yhc-table :config="tableConfig" />
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px' },
    {
      key: 'amount',
      title: '金额',
      width: '100px',
      align: 'right',
      formatter: (value) => `¥${value}`
    },
    {
      key: 'status',
      title: '状态',
      width: '80px',
      formatter: (value) => value === 1 ? '启用' : '禁用'
    }
  ]
}
</script>
```

### 文字换行控制

```vue
<template>
  <yhc-table :config="tableConfig" />
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px' },
    { key: 'description', title: '描述', width: '200px' }
  ],
  wordWrap: true // 启用文字换行，长文本会自动换行显示
}
</script>
```

### 横屏展示功能

移动端表格的横屏展示功能，采用多种技术方案确保在不同设备上都能提供良好的横屏体验。

```vue
<template>
  <yhc-table :config="tableConfig" />
</template>

<script setup>
const tableConfig = {
  columns: [
    { key: 'name', title: '名称', width: '120px' },
    { key: 'type', title: '类型', width: '150px' },
    { key: 'amount', title: '金额', width: '100px' }
  ],
  landscape: {
    enabled: true,    // 启用横屏功能
    autoRotate: true  // 自动旋转屏幕
  }
}
</script>
```

#### 技术实现方案

**方案1: Screen Orientation API + 全屏模式（推荐）**
- 支持的浏览器：现代Chrome、Firefox等
- 功能：自动锁定屏幕方向 + 全屏显示
- 体验：最佳，完全自动化

**方案2: CSS3 Transform旋转**
- 兼容性：所有现代浏览器
- 功能：通过CSS旋转实现横屏效果
- 体验：良好，需要用户配合旋转设备

**方案3: 媒体查询 + 用户提示**
- 兼容性：所有浏览器
- 功能：检测屏幕方向并提示用户
- 体验：基础，完全依赖用户操作

#### 功能特性

- **一键横屏**: 点击横屏按钮即可进入全屏横屏模式
- **智能适配**: 根据不同设备和浏览器提供最佳的横屏体验
- **自动旋转**: Android Chrome 支持自动锁定屏幕方向
- **友好提示**: iOS 和其他设备提供用户友好的操作指导
- **样式优化**: 横屏模式下自动调整字体大小和间距
- **状态指示**: 实时显示横屏状态和操作提示
- **完美滚动**: 横屏模式下使用Flex布局，确保内容完整显示，分页加载正常工作
- **内容无遮挡**: 解决了横屏模式下内容被遮挡的问题，无需双指滑动即可查看全部内容

#### 设备兼容性

| 设备/浏览器 | 自动旋转 | 全屏模式 | 用户体验 |
|------------|---------|---------|---------|
| Android Chrome | ✅ 支持 | ✅ 支持 | 最佳 |
| iOS Safari | ❌ 需手动 | ✅ 支持 | 良好 |
| 微信内置浏览器 | ❌ 需手动 | ✅ 支持 | 良好 |
| 其他移动浏览器 | ❌ 需手动 | ✅ 支持 | 良好 |

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| config | 表格配置 | object | - |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| rowClick | 行点击事件 | (item, index) |
| load | 数据加载事件 | - |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| refresh | 刷新表格 | - |

### Slots

| 名称 | 说明 | 参数 |
|------|------|------|
| [column.slot] | 自定义列内容 | { item, value, rowIndex, column } |

### Config 配置

```js
{
  // 表格列配置
  columns: [
    {
      key: 'name',        // 数据字段名或函数
      title: '姓名',      // 表头显示文字
      width: '100px',     // 列宽
      minWidth: '80px',   // 最小宽度
      align: 'left',      // 对齐方式: left/center/right
      slot: 'name',       // 自定义插槽名
      formatter: null     // 格式化函数
    }
  ],
  
  // 表格设置
  showHeader: true,       // 是否显示表头
  stripe: true,           // 是否显示斑马纹
  border: true,           // 是否显示边框
  wordWrap: true,         // 是否启用文字换行

  // 横屏展示配置
  landscape: {
    enabled: true,        // 是否启用横屏功能
    autoRotate: true      // 是否自动旋转屏幕
  },
  
  // 数据接口
  curl: {
    ls: '',               // 列表接口
  },
  postData: {},           // 请求参数
  mockData: false,        // 是否使用模拟数据
  
  // 分页配置
  pagination: {
    enabled: true,        // 无条件启用，无需配置（保留仅用于兼容性）
    pageSize: 20         // 每页数量
  },
  
  // 骨架屏配置
  skeleton: {
    isShow: false,        // 是否启用骨架屏
    count: 5,             // 骨架屏数量
    row: 1,               // 每个骨架屏的行数
    rowWidth: ['100%'],   // 每行的宽度
    duration: 2000        // 骨架屏显示时长(毫秒)
  }
}
```

## 列配置详解

### key 字段
- **字符串**: 直接对应数据对象的属性名
- **函数**: 自定义取值逻辑，参数为行数据对象

### formatter 格式化
用于格式化显示值，常用场景：
- 金额格式化: `(value) => ¥${value}`
- 状态转换: `(value) => value === 1 ? '启用' : '禁用'`
- 日期格式化: `(value) => dayjs(value).format('YYYY-MM-DD')`

### slot 自定义插槽
当需要复杂的自定义渲染时使用，插槽参数：
- `item`: 当前行数据
- `value`: 当前列的值
- `rowIndex`: 行索引
- `column`: 列配置

## 特性

- 🎨 **移动端优化** - 专为移动端设计的表格样式
- 📱 **响应式布局** - 支持不同屏幕尺寸
- 🎭 **骨架屏支持** - 提升加载体验
- 🔄 **分页加载** - 默认启用无限滚动加载，支持大数据量展示
- 🎯 **自定义列** - 灵活的列配置和插槽
- 📊 **数据格式化** - 内置格式化函数支持
- 🎪 **交互友好** - 行点击、悬停效果
- 🌐 **横屏模式** - 完美支持横屏展示，解决内容遮挡问题

## 分页加载说明

### 工作原理
- **无条件启用**: 分页加载现在无条件启用，无需任何配置即可使用
- **快速加载**: 模拟数据延迟仅200毫秒，提供快速响应体验
- **无限滚动**: 当用户滚动到底部时，自动加载下一页数据
- **模拟数据**: 如果没有配置API接口，会自动使用内置的3页模拟数据
- **真实API**: 配置 `curl.ls` 接口地址后，会调用真实API进行分页加载

### 查看效果
1. **自动加载**: 组件挂载后立即开始加载第一页数据，无需任何操作
2. **控制台日志**: 打开浏览器开发者工具，查看Console面板
3. **加载过程**: 可以看到"✅ 已加载第X页数据"的日志信息
4. **滚动测试**: 向下滚动表格，观察新数据的自动加载
5. **结束状态**: 当显示"没有更多了"时，表示所有数据已加载完成

### 调试信息
```javascript
// 在控制台可以看到以下信息：
"yhc-table组件初始化，开始加载数据..."
"分页配置: {enabled: true, pageSize: 20}"
"🚀 onLoad被调用，当前状态: loading=false, finished=false, page=1"
"⏳ 开始加载第1页数据...（loading状态由van-list管理）"
"✅ 已加载第1页数据，共5条，总数据量：5"
"📊 加载完成，当前状态: loading=false, finished=false, 总数据：5条"
"🔄 nextTick后状态: loading=false, finished=false"
"🎯 第一页加载完成，van-list已准备好下次加载"
"🚀 onLoad被调用，当前状态: loading=true, finished=false, page=2"
"⏳ 开始加载第2页数据...（loading状态由van-list管理）"
"✅ 已加载第2页数据，共4条，总数据量：9"
"🎉 模拟数据加载完成，没有更多数据"
```

## 最佳实践

1. **列宽设置**: 建议为重要列设置固定宽度，避免内容挤压
2. **数据格式化**: 优先使用formatter，复杂渲染使用slot
3. **骨架屏**: 在网络较慢时启用，提升用户体验
4. **分页加载**: 无条件启用，开箱即用，自动处理大数据量加载
5. **移动端适配**: 控制列数量，多列数据建议使用横屏模式
6. **横屏模式**: 数据列较多时建议启用横屏功能，提供更好的浏览体验
7. **性能优化**: 大数据量时合理设置 `pageSize`，避免单次加载过多数据
