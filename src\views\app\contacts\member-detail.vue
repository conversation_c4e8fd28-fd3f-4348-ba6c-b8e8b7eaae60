<template>
  <div class="member-detail-page">
    <!-- 表单内容 -->
    <div class="form-container">
      <div class="group-wrapper">
        <van-field v-model="memberName" label="姓名" class="form-field" readonly />
        <van-field v-model="memberPhone" label="手机号" class="form-field" readonly />
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div class="bottom-action">
      <div class="button-group">
        <van-button type="default" size="large" class="delete-btn" @click="handleResign">
          删除
        </van-button>
        <van-button type="primary" size="large" class="modify-btn" @click="handleModify">
          修改
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast, showSuccessToast, showConfirmDialog } from 'vant'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const memberName = ref('')
const memberPhone = ref('')

// 辞退人员
const handleResign = async () => {
  try {
    await showConfirmDialog({
      title: '辞退人员',
      message: '是否确定辞退人员？',
      confirmButtonText: '辞退人员',
      cancelButtonText: '取消',
      confirmButtonColor: '#ee0a24'
    })

    console.log('辞退人员:', {
      id: route.query.id,
      name: memberName.value,
      phone: memberPhone.value
    })

    showSuccessToast('人员辞退成功')

    // 延迟返回上一页
    setTimeout(() => {
      router.back()
    }, 1500)
  } catch (error) {
    // 用户取消辞退
    console.log('取消辞退')
  }
}

// 修改人员
const handleModify = () => {
  // 跳转到修改人员页面（可以复用添加人员页面）
  router.push({
    name: 'add-member',
    query: {
      id: route.query.id,
      name: memberName.value,
      phone: memberPhone.value,
      deptId: route.query.deptId,
      deptName: route.query.deptName,
      mode: 'edit' // 标识为编辑模式
    }
  })
}

// 加载人员数据
const loadMemberData = () => {
  // 从路由参数获取人员信息
  memberName.value = route.query.name || '徐星辰'
  memberPhone.value = route.query.phone || '15003903233'
}

// 页面挂载时加载数据
onMounted(() => {
  loadMemberData()
})
</script>

<style lang="scss" scoped>
.member-detail-page {
  min-height: 100vh;
  background: #f2f3f4;
  display: flex;
  flex-direction: column;
  padding-bottom: 100px; // 为底部固定按钮留出空间
}

.form-container {
  flex: 1;
  background: #f2f3f4;
  box-sizing: border-box;
  border-top: 0.5px solid #f2f3f4;
  padding-bottom: 0px;

  .group-wrapper {
    margin: 16px;
    border-radius: 8px;
    overflow: hidden;
    background: white;

    .form-field {
      font-size: 16px;
      padding: 16px;

      :deep(.van-field__label) {
        color: #323233;
        font-weight: 500;
      }

      :deep(.van-field__control) {
        color: #323233;

        &[readonly] {
          background: transparent;
          color: #323233;
        }
      }
    }
  }
}

.bottom-action {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 999;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;

  .button-group {
    display: flex;
    gap: 12px;

    .delete-btn {
      flex: 1;
      height: 48px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      background: transparent;
      border: 1px solid #e0e2e4;
      color: #646566;

      &:hover {
        background: #f7f8fa;
      }
    }

    .modify-btn {
      flex: 1;
      height: 48px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
