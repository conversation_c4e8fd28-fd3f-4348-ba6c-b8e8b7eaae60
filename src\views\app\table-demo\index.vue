<template>
  <div class="table-demo-container">
    <!-- 页面标题 -->
    <yhc-breadcrumb :data="breadcrumbData" />

    <!-- 控制面板 -->
    <div class="control-panel">
      <van-cell-group>
        <van-cell title="启用骨架屏" center>
          <template #right-icon>
            <van-switch v-model="tableConfig.skeleton.isShow" />
          </template>
        </van-cell>
        <van-cell title="显示表头" center>
          <template #right-icon>
            <van-switch v-model="tableConfig.showHeader" />
          </template>
        </van-cell>
        <van-cell title="启用分页" center>
          <template #right-icon>
            <van-switch v-model="tableConfig.pagination.enabled" />
          </template>
        </van-cell>
        <van-cell title="文字换行" center>
          <template #right-icon>
            <van-switch v-model="tableConfig.wordWrap" />
          </template>
        </van-cell>
        <van-cell title="横屏展示" center>
          <template #right-icon>
            <van-switch v-model="tableConfig.landscape.enabled" />
          </template>
        </van-cell>
      </van-cell-group>

      <div class="button-group">
        <van-notice-bar left-icon="info-o" text="修改配置后，点击下方按钮重新加载表格查看效果" :scrollable="false" />
        <van-button type="primary" block @click="refreshTable" style="margin-top: 12px;">
          重新加载表格
        </van-button>
      </div>
    </div>

    <!-- 表格演示 -->
    <div class="table-wrapper">
      <yhc-table :key="tableKey" :config="tableConfig" @rowClick="onRowClick" ref="tableRef">
        <!-- 自定义名称列 -->
        <template #name="{ item, value }">
          <div class="name-cell">
            <span class="name-text">{{ value }}</span>
            <van-tag v-if="item.type === '学生'" type="primary" size="mini">学生</van-tag>
          </div>
        </template>

        <!-- 自定义金额列 -->
        <template #amount="{ value }">
          <span class="amount-text" :class="{ 'amount-zero': parseFloat(value) === 0 }">
            ¥{{ value }}
          </span>
        </template>

        <!-- 自定义操作列 -->
        <template #action="{ item, rowIndex }">
          <van-button type="primary" size="mini" @click.stop="handleAction(item, rowIndex)">
            操作
          </van-button>
        </template>
      </yhc-table>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { showToast } from 'vant'
import yhcTable from '@/components/yhc-table'
import yhcBreadcrumb from '@/components/yhc-breadcrumb'

// 面包屑数据
const breadcrumbData = [
  { text: '工作台', url: '/app' },
  { text: '表格演示' }
]

// 表格引用
const tableRef = ref(null)
const tableKey = ref(0)

// 表格配置
const tableConfig = reactive({
  columns: [
    {
      key: 'name',
      title: '类型',
      width: '100px',
      slot: 'name',
      align: 'left'
    },
    {
      key: 'type',
      title: '账户类型',
      width: '120px',
      align: 'center'
    },
    {
      key: 'count',
      title: '数量',
      width: '60px',
      align: 'center'
    },
    {
      key: 'amount1',
      title: '应收金额',
      width: '80px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'amount2',
      title: '实收金额',
      width: '80px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'count2',
      title: '应收笔数',
      width: '70px',
      align: 'center'
    },
    {
      key: 'count3',
      title: '实收笔数',
      width: '70px',
      align: 'center'
    },
    {
      key: 'amount3',
      title: '应付金额',
      width: '80px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'amount4',
      title: '应付余额',
      width: '80px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'amount5',
      title: '应付充值金额',
      width: '100px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'amount6',
      title: '应付充值余额',
      width: '100px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'amount7',
      title: '应付充值金额',
      width: '100px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'amount8',
      title: '应付充值余额',
      width: '100px',
      align: 'right',
      slot: 'amount'
    },
    {
      key: 'action',
      title: '操作',
      width: '80px',
      align: 'center',
      slot: 'action'
    }
  ],

  showHeader: true,
  stripe: true,
  border: true,
  wordWrap: true,
  landscape: {
    enabled: true,
    autoRotate: true
  },

  curl: {
    ls: '' // 使用模拟数据
  },
  mockData: true,

  pagination: {
    enabled: false,
    pageSize: 20
  },

  skeleton: {
    isShow: true,
    count: 8,
    row: 1,
    rowWidth: ['100%'],
    duration: 2000
  }
})

// 行点击事件
const onRowClick = (item, index) => {
  showToast(`点击了第${index + 1}行: ${item.name}`)
}

// 操作按钮点击
const handleAction = (item, index) => {
  showToast(`操作: ${item.name}`)
}

// 刷新表格
const refreshTable = () => {
  showToast('表格重新加载中...')

  if (tableRef.value && tableRef.value.refresh) {
    tableRef.value.refresh()
  } else {
    // 备用方案：强制重新渲染
    tableKey.value = Date.now()
  }

  setTimeout(() => {
    showToast('表格加载完成')
  }, tableConfig.skeleton.duration + 500)
}
</script>

<style lang="scss" scoped>
.table-demo-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding-bottom: 80px;
}

.control-panel {
  margin: 16px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;

  .button-group {
    padding: 16px;
  }
}

.table-wrapper {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.name-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .name-text {
    flex: 1;
    font-weight: 500;
  }
}

.amount-text {
  font-weight: 500;
  color: #323233;

  &.amount-zero {
    color: #969799;
  }
}
</style>
