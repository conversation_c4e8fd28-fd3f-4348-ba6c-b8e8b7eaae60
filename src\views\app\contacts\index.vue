<template>
  <div class="contacts-page">
    <!-- 顶部搜索栏 -->
    <div class="search-header">
      <div class="search-bar">
        <van-search v-model="searchValue" placeholder="搜索" :show-action="false" background="#f7f8fa"
          @input="onSearch" />
      </div>
    </div>

    <!-- 公司名称 -->
    <div class="company-name">
      河南一科技有限公司
    </div>

    <!-- 组织架构列表 -->
    <div class="org-list" v-if="currentDepartments.length > 0 || externalDepartments.length > 0">
      <!-- 内部部门列表 -->
      <div v-for="dept in currentDepartments" :key="'dept-' + dept.id" class="org-item department-item"
        @click="handleDepartmentClick(dept)">
        <div class="item-icon">
          <van-icon name="cluster-o" size="16" color="#1989fa" />
        </div>
        <div class="item-info">
          <div class="item-name">
            {{ dept.name }}
            <span class="member-count">({{ dept.memberCount || 0 }})</span>
          </div>
        </div>
        <van-icon name="arrow" class="item-arrow" />
      </div>

      <!-- 外部部门分组 -->
      <div v-if="externalDepartments.length > 0" class="external-section">
        <div class="section-title">外部部门</div>
        <div v-for="dept in externalDepartments" :key="'external-' + dept.id"
          class="org-item department-item external-item" @click="handleDepartmentClick(dept)">
          <div class="item-icon">
            <van-icon name="cluster-o" size="16" color="#1989fa" />
          </div>
          <div class="item-info">
            <div class="item-name">
              {{ dept.name }}
              <span class="member-count">({{ dept.memberCount || 0 }})</span>
            </div>
          </div>
          <van-icon name="arrow" class="item-arrow" />
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <van-empty description="暂无数据" />
    </div>

    <!-- 固定底部的操作按钮 -->
    <div class="fixed-bottom-section">
      <div class="bottom-buttons">
        <div class="bottom-button" @click="handleAddExternalMember">
          <span>添加外部人员</span>
        </div>
        <div class="bottom-button" @click="handleAddExternalDepartment">
          <span>添加外部部门</span>
        </div>
      </div>
    </div>

    <!-- 同步组织弹窗 -->
    <van-dialog v-model:show="showSyncDialog" :show-cancel-button="false" confirm-button-text="我知道了"
      confirm-button-color="#1989fa" class="sync-dialog" @confirm="closeSyncDialog">
      <div class="sync-dialog-content">
        <div class="sync-title">同步组织</div>
        <div class="sync-message">
          前往钉钉管理后台(https://oa.dingtalk.com)->工作台<br>
          ->应用管理->云一消费->设置->应用可见范围<br>
          (确认应用可见范围涵盖本次同步的部门与人员)
        </div>
      </div>
    </van-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import { useLoginStore } from '@/store/dingLogin'
import { showToast } from 'vant'

// 联系人页面 - 用于显示和管理企业组织架构
const app = useLoginStore()
const { proxy } = getCurrentInstance()

// 页面状态管理
const searchValue = ref('')        // 搜索值 (字符串) - 用户输入的搜索关键词
const showSyncDialog = ref(false)  // 同步弹窗显示状态



// 显示数据
const currentDepartments = ref([]) // 当前显示的部门 (数组) - 当前层级的子部门列表
const externalDepartments = ref([]) // 外部部门 (数组) - 外包项目团队等外部部门

// 组织架构完整数据
const orgData = ref({
  departments: [],       // 部门数据 (数组) - 完整的部门树形结构
  members: []           // 成员数据 (数组) - 所有成员的扁平列表
})

// 数据结构说明:
// 部门数据格式: {id: 1, name: "技术部", parentId: 0, children: [...]}
// 成员数据格式: {id: 1, name: "张三", title: "工程师", deptId: 1, isManager: false, isOwner: false}

// 搜索功能
const onSearch = (value) => {
  if (!value.trim()) {
    loadDepartmentData(currentDeptId.value)
    return
  }

  // 实现搜索逻辑
  const searchResults = searchInOrg(value.trim())
  currentDepartments.value = searchResults.departments
}

// 在组织架构中搜索
const searchInOrg = (keyword) => {
  const results = {
    departments: [],
    members: []
  }

  // 搜索部门
  const searchDepts = (depts) => {
    depts.forEach(dept => {
      if (dept.name.includes(keyword)) {
        results.departments.push(dept)
      }
      if (dept.children && dept.children.length > 0) {
        searchDepts(dept.children)
      }
    })
  }

  // 搜索成员
  const searchMembers = (members) => {
    members.forEach(member => {
      if (member.name.includes(keyword) || (member.title && member.title.includes(keyword))) {
        results.members.push(member)
      }
    })
  }

  searchDepts(orgData.value.departments)
  searchMembers(orgData.value.members)

  return results
}

// 处理部门点击
const handleDepartmentClick = (dept) => {
  // 跳转到部门详情页面
  proxy.$router.push({
    name: 'department',
    query: {
      id: dept.id,
      name: dept.name
    }
  })
}



// 选择成员 - 页面内部处理
const selectMember = (member) => {
  showToast({
    message: `已选择成员: ${member.name}`,
    type: 'success'
  })
  console.log('选择了成员:', member)
}



// 添加外部人员 - 跳转到添加人员页面
const handleAddExternalMember = () => {
  // 跳转到添加人员页面，标识这是添加外部人员
  proxy.$router.push({
    name: 'add-member',
    query: {
      type: 'external', // 标识这是添加外部人员
      deptName: '河南一科技有限公司' // 所属公司
    }
  })
}

// 添加外部部门 - 跳转到添加部门页面
const handleAddExternalDepartment = () => {
  // 跳转到添加部门页面，不传父部门参数表示添加外部部门
  proxy.$router.push({
    name: 'add-department',
    query: {
      type: 'external', // 标识这是添加外部部门
      parentName: '河南一科技有限公司' // 父级为公司
    }
  })
}

// 加载部门数据
const loadDepartmentData = async (deptId = 0, forceAPI = false) => {
  try {
    // 可以通过forceAPI参数强制使用API
    const useRealAPI = forceAPI || false // 设置为true时使用钉钉API

    if (useRealAPI && (app.browserEnv === "dd" || app.browserEnv === "wx" || app.corpId)) {
      // 使用钉钉/企业微信API
      const apiData = await loadFromDingTalkAPI(deptId)
      currentDepartments.value = apiData.departments
      currentMembers.value = apiData.members
    } else {
      // 使用模拟数据
      const mockData = getMockOrgData(deptId)
      currentDepartments.value = mockData.departments
      // 加载外部部门数据
      externalDepartments.value = getExternalDepartments()
    }
  } catch (error) {
    console.error('加载组织架构数据失败:', error)
    // 失败时回退到模拟数据
    const mockData = getMockOrgData(deptId)
    currentDepartments.value = mockData.departments
  }
}

// 模拟数据 - 基于钉钉截图的真实数据结构
const getMockOrgData = (deptId) => {
  const mockData = {
    0: { // 根部门 - 对应"河南一科技有限公司"
      departments: [
        { id: 1, name: '总经办', memberCount: 8 },
        { id: 2, name: '钉钉事业部', memberCount: 8 },
        { id: 3, name: '业务拓展部', memberCount: 8 },
        { id: 4, name: '产品事业部', memberCount: 8 },
        { id: 5, name: '综合部', memberCount: 8 },
        { id: 6, name: '财务部', memberCount: 8 }
      ],
      members: [
        {
          id: 'user1',
          name: '梁峰源',
          title: '主管理员',
          position: '董事长',
          avatar: '',
          isManager: true,
          isOwner: true
        }
      ]
    },
    4: { // 产品事业部
      departments: [
        { id: 41, name: '产品研发A组', memberCount: 8 },
        { id: 42, name: '产品研发B组', memberCount: 6 },
        { id: 43, name: '创新实验组', memberCount: 5 },
        { id: 44, name: '定制开发组', memberCount: 7 },
        { id: 45, name: 'AI智能组', memberCount: 4 },
        { id: 46, name: '测试质量组', memberCount: 6 },
        { id: 47, name: '运维保障组', memberCount: 4 },
        { id: 48, name: '产品设计组', memberCount: 5 },
        { id: 49, name: '前端开发组', memberCount: 8 },
        { id: 50, name: '后端开发组', memberCount: 10 },
        { id: 51, name: '移动开发组', memberCount: 6 },
        { id: 52, name: '数据分析组', memberCount: 4 }
      ],
      members: [
        {
          id: 'user2',
          name: '张振北',
          title: '不管理员',
          position: '部长',
          avatar: '',
          isManager: false
        },
        {
          id: 'user3',
          name: '杨富强',
          title: '设定距',
          position: '测试工程师',
          avatar: ''
        },
        {
          id: 'user4',
          name: '董鹏飞',
          title: '',
          position: '安全工程师',
          avatar: ''
        },
        {
          id: 'user5',
          name: '段启祥',
          title: '',
          position: '前端工程师',
          avatar: ''
        },
        {
          id: 'user6',
          name: '王佳文',
          title: '不管理员',
          position: 'UI设计师',
          avatar: ''
        },
        {
          id: 'user7',
          name: '李明华',
          title: '高级工程师',
          position: 'Java开发工程师',
          avatar: ''
        },
        {
          id: 'user8',
          name: '陈小红',
          title: '产品经理',
          position: '产品经理',
          avatar: ''
        },
        {
          id: 'user9',
          name: '刘建国',
          title: '架构师',
          position: '系统架构师',
          avatar: ''
        },
        {
          id: 'user10',
          name: '赵丽娟',
          title: '运营专员',
          position: '运营专员',
          avatar: ''
        },
        {
          id: 'user11',
          name: '孙志强',
          title: 'DevOps工程师',
          position: 'DevOps工程师',
          avatar: ''
        },
        {
          id: 'user12',
          name: '周美玲',
          title: '数据分析师',
          position: '数据分析师',
          avatar: ''
        },
        {
          id: 'user13',
          name: '吴建军',
          title: 'Python开发',
          position: 'Python开发工程师',
          avatar: ''
        },
        {
          id: 'user14',
          name: '郑晓燕',
          title: 'UI设计师',
          position: 'UI/UX设计师',
          avatar: ''
        },
        {
          id: 'user15',
          name: '马云飞',
          title: '移动端开发',
          position: 'iOS开发工程师',
          avatar: ''
        },
        {
          id: 'user16',
          name: '林小芳',
          title: '移动端开发',
          position: 'Android开发工程师',
          avatar: ''
        },
        {
          id: 'user17',
          name: '黄志伟',
          title: '全栈工程师',
          position: '全栈开发工程师',
          avatar: ''
        },
        {
          id: 'user18',
          name: '许文静',
          title: '测试工程师',
          position: '自动化测试工程师',
          avatar: ''
        },
        {
          id: 'user19',
          name: '何建华',
          title: '运维工程师',
          position: '云平台运维工程师',
          avatar: ''
        },
        {
          id: 'user20',
          name: '谢丽丽',
          title: '项目经理',
          position: '项目经理',
          avatar: ''
        }
      ]
    }
  }

  return mockData[deptId] || { departments: [], members: [] }
}

// 获取外部部门数据
const getExternalDepartments = () => {
  return [
    { id: 'ext1', name: '外包项目一团队', memberCount: 8 },
    { id: 'ext2', name: '外包项目二团队', memberCount: 6 }
  ]
}

// 集成钉钉API的数据加载函数
const loadFromDingTalkAPI = async (deptId = 0) => {
  if (app.browserEnv === "wx") {
    // 企业微信环境
    return new Promise((resolve, reject) => {
      proxy.$_dd.selectEnterpriseContact({
        fromDepartmentId: deptId,
        mode: "multi",
        type: ["department", "user"],
        success (res) {
          const result = res.result
          const departments = result.departmentList.map(dept => ({
            id: dept.id,
            name: dept.name,
            memberCount: dept.memberCount || 0
          }))
          const members = result.userList.map(user => ({
            id: user.id,
            name: user.name,
            title: user.title || '',
            position: user.position || '员工',
            avatar: user.avatar || '',
            isManager: user.isManager || false,
            isOwner: user.isOwner || false
          }))
          resolve({ departments, members })
        },
        fail (err) {
          console.error('企业微信获取组织架构失败:', err)
          reject(err)
        }
      })
    })
  } else if (app.browserEnv === "dd") {
    // 钉钉环境 - 使用部门和用户API分别获取数据
    return new Promise((resolve, reject) => {
      proxy.$_dd.ready(function () {
        // 先获取部门列表
        proxy.$_dd.biz.contact.departmentsPicker({
          title: '获取部门信息',
          corpId: app.corpId,
          multiple: true,
          appId: app.appid,
          fromDepartmentId: deptId,
          onSuccess: function (deptResult) {
            // 再获取用户列表
            proxy.$_dd.biz.contact.complexChoose({
              title: '获取用户信息',
              corpId: app.corpId,
              multiple: true,
              maxUsers: 10000,
              appId: app.appid,
              fromDepartmentId: deptId,
              onSuccess: function (userResult) {
                const departments = deptResult.departments ? deptResult.departments.map(dept => ({
                  id: dept.id,
                  name: dept.name,
                  memberCount: dept.memberCount || 0,
                  parentId: dept.parentId
                })) : []

                const members = userResult.users ? userResult.users.map(user => ({
                  id: user.emplId || user.id,
                  name: user.name,
                  title: user.title || '',
                  position: user.position || '员工',
                  avatar: user.avatar || '',
                  isManager: user.isManager || false,
                  isOwner: user.isOwner || false
                })) : []

                resolve({ departments, members })
              },
              onFail: function (err) {
                console.error('钉钉获取用户信息失败:', err)
                // 如果获取用户失败，至少返回部门信息
                const departments = deptResult.departments ? deptResult.departments.map(dept => ({
                  id: dept.id,
                  name: dept.name,
                  memberCount: dept.memberCount || 0,
                  parentId: dept.parentId
                })) : []
                resolve({ departments, members: [] })
              }
            })
          },
          onFail: function (err) {
            console.error('钉钉获取部门信息失败:', err)
            reject(err)
          }
        })
      })
    })
  } else {
    // 其他环境，返回空数据
    return Promise.resolve({ departments: [], members: [] })
  }
}

// 设置钉钉右上角同步按钮
const setRightButton = () => {
  if (app.browserEnv === "dd" && proxy.$_dd) {
    proxy.$_dd.biz.navigation.setRight({
      text: '同步',
      control: true,
      onSuccess: function () {
        syncOrgData()
      },
      onFail: function (err) {
        console.error('设置钉钉右上角按钮失败:', err)
      }
    })
  }
}

// 同步组织架构数据
const syncOrgData = async () => {
  // 显示同步说明弹窗
  showSyncDialog.value = true
}

// 关闭同步弹窗
const closeSyncDialog = () => {
  showSyncDialog.value = false
}



// 页面挂载时加载根部门数据并设置钉钉按钮
onMounted(() => {
  loadDepartmentData(0)
  // 延迟设置钉钉按钮，确保钉钉环境已准备就绪
  setTimeout(() => {
    setRightButton()
  }, 500)
})
</script>

<style lang="scss" scoped>
::v-deep {
  .van-dialog__footer {
    padding: 0 !important;
    margin-top: 24px !important;
  }
}

.contacts-page {
  height: 100vh;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  overflow: hidden;
  /* 钉钉内部H5优化 */
  position: relative;
  touch-action: manipulation;
}

.search-header {
  background: white;
  padding: 8px 16px;
  border-bottom: 1px solid #ebedf0;

  .search-bar {
    :deep(.van-search) {
      padding: 0;
    }
  }
}

.company-name {
  background: white;
  padding: 16px 16px;
  border-bottom: 1px solid #ebedf0;
  font-size: 14px;
  color: #646566;
  font-weight: 500;
}

.org-list {
  flex: 1;
  overflow-y: auto;
  background: white;
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  /* 确保在移动设备上可以滚动 */
  touch-action: pan-y;
  /* 钉钉内部H5滚动优化 */
  overscroll-behavior: contain;
  /* 为固定底部按钮留出空间 */
  padding-bottom: 80px;

  .external-section {
    .section-title {
      padding: 12px 16px 8px;
      font-size: 14px;
      color: #969799;
      background: #f7f8fa;
      padding-top: 16px;
      padding-bottom: 16px;
      padding-left: 32px;
    }

    .external-item {
      background: white; // 外部部门保持白色背景，和内部部门一样
    }
  }

  .org-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f7f8fa;
    cursor: pointer;

    &:hover {
      background: #f7f8fa;
    }

    .item-icon {
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .item-info {
      flex: 1;

      .item-name {
        font-size: 16px;
        color: #323233;
        margin-bottom: 2px;
        display: flex;
        align-items: center;

        .member-count {
          font-size: 14px;
          color: #969799;
          margin-left: 4px;
        }
      }

      .item-desc {
        font-size: 12px;
        color: #969799;
      }
    }

    .item-arrow {
      color: #c8c9cc;
    }

    // 人员特有样式
    &.member-item {
      .member-avatar {
        .default-avatar {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          background: #1989fa;
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .member-tags {
        display: flex;
        gap: 4px;
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  min-height: 60vh;

  :deep(.van-empty) {
    padding: 0px 20px;
  }
}

// 固定底部的操作按钮
.fixed-bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ebedf0;
  z-index: 100;
  height: 86px;

  .bottom-buttons {
    display: flex;
    height: 100%;

    .bottom-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:first-child::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 22px;
        background: #d1d5db;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &:active {
        background: rgba(255, 255, 255, 0.2);
      }

      span {
        color: #1989fa;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

// 同步弹窗样式
:deep(.sync-dialog) {
  .van-dialog {
    width: 311px !important;
    height: 206px !important;
    border-radius: 12px;
  }

  .van-dialog__content {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .sync-dialog-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 24px 20px 0;

    .sync-title {
      font-size: 16px;
      color: #323233;
      text-align: center;
      margin-bottom: 16px;
      font-weight: 500;
    }

    .sync-message {
      width: 263px;
      height: 80px;
      font-size: 12px;
      color: #969799;
      line-height: 1.5;
      text-align: center;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-left: 12px;
      padding-right: 12px;
    }
  }

  .van-dialog__footer {
    padding: 16px 20px 20px;
    border-top: none;

    .van-button {
      height: 48px;
      border-radius: 6px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>
