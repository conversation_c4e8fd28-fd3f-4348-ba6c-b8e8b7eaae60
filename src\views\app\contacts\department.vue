<template>
  <div class="department-page">
    <!-- 顶部搜索栏 -->
    <div class="search-header">
      <div class="search-bar">
        <van-search v-model="searchValue" placeholder="搜索" :show-action="false" background="#f7f8fa"
          @input="onSearch" />
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav">
      <div class="nav-path">
        <span class="nav-item" @click="goBack">河南一科技有限公司</span>
        <van-icon name="arrow" class="nav-arrow" />
        <span class="nav-item active">{{ departmentName }}</span>
      </div>
    </div>

    <!-- 人员列表 -->
    <div class="member-list" v-if="filteredMembers.length > 0">
      <div v-for="member in filteredMembers" :key="member.id" class="member-item" @click="handleMemberClick(member)">
        <div class="member-avatar">
          <van-image v-if="member.avatar" :src="member.avatar" round width="40" height="40" fit="cover" />
          <div v-else class="default-avatar">
            {{ member.name.charAt(0) }}
          </div>
        </div>
        <div class="member-info">
          <div class="member-name">{{ member.name }}</div>
          <div class="member-tags">
            <!-- 状态标签 -->
            <van-tag v-if="member.status === 'inactive'" class="inactive-tag" size="mini">
              已离职
            </van-tag>
            <!-- 其他标签（只在在职时显示） -->
            <van-tag v-else v-for="tag in member.tags" :key="tag" :type="getTagType(tag)" size="mini">
              {{ tag }}
            </van-tag>
          </div>
        </div>
        <div class="member-actions">
          <div class="qr-icon" @click.stop="showQRCode(member)">
            <img src="/svg/qr.svg" alt="二维码" />
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <van-empty description="暂无人员数据" />
    </div>

    <!-- 二维码弹窗 -->
    <van-dialog v-model:show="showQRDialog" :show-cancel-button="false" :show-confirm-button="false" class="qr-dialog"
      @close="closeQRDialog">
      <div class="qr-dialog-content">
        <div class="qr-close-btn" @click="closeQRDialog">
          <van-icon name="cross" size="20" color="#969799" />
        </div>
        <div class="qr-title">用户付款码</div>
        <div class="qr-username">{{ currentUser.name }}</div>
        <div class="qr-code-container">
          <img src="/img/yykj.png" alt="二维码" class="qr-code-image" />
        </div>
        <div class="qr-tip">长摁二维码保存到相册</div>
      </div>
    </van-dialog>

    <!-- 固定底部的操作按钮 -->
    <div class="fixed-bottom-section">
      <div class="bottom-buttons">
        <div class="bottom-button" @click="handleAddMember">
          <span>添加人员</span>
        </div>
        <div class="bottom-button" @click="handleAddSubDepartment">
          <span>添加子部门</span>
        </div>
        <div class="bottom-button" @click="handleDepartmentSettings">
          <span>部门设置</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

// 路由相关
const route = useRoute()
const router = useRouter()

// 页面状态
const searchValue = ref('')
const departmentName = ref('')
const members = ref([])
const showQRDialog = ref(false)  // 二维码弹窗显示状态
const currentUser = ref({})      // 当前查看二维码的用户

// 计算属性 - 过滤后的成员列表
const filteredMembers = computed(() => {
  if (!searchValue.value) {
    return members.value
  }
  return members.value.filter(member =>
    member.name.toLowerCase().includes(searchValue.value.toLowerCase())
  )
})

// 搜索功能
const onSearch = (value) => {
  console.log('搜索:', value)
}

// 返回上一页
const goBack = () => {
  router.back()
}

// 获取标签类型
const getTagType = (tag) => {
  switch (tag) {
    case '主管':
      return 'success'
    case '蔚迟':
      return 'primary'
    case '康敏':
      return 'warning'
    default:
      return 'primary'
  }
}

// 显示二维码
const showQRCode = (member) => {
  currentUser.value = member
  showQRDialog.value = true
}

// 关闭二维码弹窗
const closeQRDialog = () => {
  showQRDialog.value = false
  currentUser.value = {}
}

// 处理人员点击
const handleMemberClick = (member) => {
  if (member.status === 'inactive') {
    // 已离职人员，跳转到恢复页面
    router.push({
      name: 'member-restore',
      query: {
        id: member.id,
        name: member.name,
        phone: member.phone,
        deptId: route.query.id,
        deptName: departmentName.value
      }
    })
  } else {
    // 在职人员，跳转到详情页面
    router.push({
      name: 'member-detail',
      query: {
        id: member.id,
        name: member.name,
        phone: member.phone,
        deptId: route.query.id,
        deptName: departmentName.value
      }
    })
  }
}

// 添加人员
const handleAddMember = () => {
  // 跳转到添加人员页面
  router.push({
    name: 'add-member',
    query: {
      deptId: route.query.id,
      deptName: departmentName.value
    }
  })
}

// 添加子部门
const handleAddSubDepartment = () => {
  // 跳转到添加子部门页面
  router.push({
    name: 'add-department',
    query: {
      parentId: route.query.id,
      parentName: departmentName.value
    }
  })
}

// 部门设置
const handleDepartmentSettings = () => {
  // 跳转到部门设置页面
  router.push({
    name: 'department-settings',
    query: {
      id: route.query.id,
      name: departmentName.value
    }
  })
}

// 加载部门数据
const loadDepartmentData = () => {
  // 从路由参数获取部门信息
  const deptId = route.params.id || route.query.id
  const deptName = route.params.name || route.query.name || '软件事业部'

  departmentName.value = deptName

  // 根据部门ID提供不同的人员数据
  if (deptId === 'ext1') {
    // 外包项目一团队的人员
    members.value = [
      {
        id: 'ext1_1',
        name: '李外包',
        avatar: '',
        tags: ['项目经理'],
        status: 'active',
        phone: '15012345678'
      },
      {
        id: 'ext1_2',
        name: '王外包',
        avatar: '',
        tags: [],
        status: 'active',
        phone: '15087654321'
      }
    ]
  } else if (deptId === 'ext2') {
    // 外包项目二团队的人员
    members.value = [
      {
        id: 'ext2_1',
        name: '张外包',
        avatar: '',
        tags: ['技术负责人'],
        status: 'active',
        phone: '15098765432'
      },
      {
        id: 'ext2_2',
        name: '刘外包',
        avatar: '',
        tags: [],
        status: 'inactive',
        phone: '15076543210'
      }
    ]
  } else {
    // 内部部门的人员数据 - 根据设计图，添加状态字段
    members.value = [
      {
        id: 1,
        name: '徐星辰',
        avatar: '',
        tags: ['主管'],
        status: 'active', // active: 在职, inactive: 已离职
        phone: '15003903233'
      },
      {
        id: 2,
        name: '蔚迟',
        avatar: '',
        tags: ['蔚迟'],
        status: 'active',
        phone: '13812345678'
      },
      {
        id: 3,
        name: '康敏',
        avatar: '',
        tags: ['康敏'],
        status: 'inactive', // 已离职状态
        phone: '13987654321'
      },
      {
        id: 4,
        name: '岑益辰',
        avatar: '',
        tags: [],
        status: 'active',
        phone: '15612345678'
      },
      {
        id: 5,
        name: '华凌晶',
        avatar: '',
        tags: [],
        status: 'active',
        phone: '13698765432'
      },
      {
        id: 6,
        name: '卜佳琪',
        avatar: '',
        tags: [],
        status: 'inactive', // 已离职状态
        phone: '15887654321'
      },
      {
        id: 7,
        name: '孙雪瑶',
        avatar: '',
        tags: [],
        status: 'active',
        phone: '13776543210'
      }
    ]
  }
}

// 页面挂载时加载数据
onMounted(() => {
  loadDepartmentData()
})
</script>

<style lang="scss" scoped>
.department-page {
  min-height: 100vh;
  background: #f7f8fa;
  display: flex;
  flex-direction: column;
  padding-bottom: 86px; // 为底部按钮留出空间
}

.search-header {
  background: white;
  padding: 8px 16px;
  border-bottom: 1px solid #f7f8fa;

  .search-bar {
    :deep(.van-search) {
      padding: 0;
      background: transparent;

      .van-search__content {
        background: #f7f8fa;
        border-radius: 8px;
      }
    }
  }
}

.breadcrumb-nav {
  background: white;
  padding: 12px 16px;
  border-bottom: 1px solid #f7f8fa;

  .nav-path {
    display: flex;
    align-items: center;
    font-size: 14px;

    .nav-item {
      color: #1989fa;
      cursor: pointer;

      &.active {
        color: #323233;
      }

      &:hover {
        opacity: 0.7;
      }
    }

    .nav-arrow {
      margin: 0 8px;
      font-size: 12px;
      color: #c8c9cc;
    }
  }
}

.member-list {
  flex: 1;
  background: white;

  .member-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f7f8fa;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f7f8fa;
    }

    &:active {
      background-color: #ebedf0;
    }

    .member-avatar {
      margin-right: 12px;

      .default-avatar {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: #1989fa;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .member-info {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 8px;

      .member-name {
        font-size: 16px;
        color: #323233;
      }

      .member-tags {
        display: flex;
        gap: 4px;

        :deep(.van-tag) {
          background: transparent;
          color: #1989fa;
          border: 1px solid #1989fa;

          &.van-tag--success {
            background: transparent;
            color: #1989fa;
            border: 1px solid #1989fa;
          }

          &.van-tag--primary {
            background: transparent;
            color: #1989fa;
            border: 1px solid #1989fa;
          }

          &.van-tag--warning {
            background: transparent;
            color: #1989fa;
            border: 1px solid #1989fa;
          }
        }

        // 已离职标签样式
        :deep(.inactive-tag) {
          background: transparent !important;
          color: #c8c9cc !important;
          border: 1px solid #c8c9cc !important;
        }
      }
    }

    .member-actions {
      .qr-icon {
        width: 24px;
        height: 24px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          width: 20px;
          height: 20px;
        }

        &:hover {
          opacity: 0.7;
        }
      }
    }
  }
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: white;
  margin-top: 8px;
  min-height: 60vh;

  :deep(.van-empty) {
    padding: 0px 20px;
  }
}

// 二维码弹窗样式
:deep(.qr-dialog) {
  .van-dialog {
    width: 311px !important;
    height: 395px !important;
    border-radius: 12px;
  }

  .van-dialog__content {
    padding: 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .qr-dialog-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    position: relative;

    .qr-close-btn {
      position: absolute;
      top: 16px;
      right: 16px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      background: #f7f8fa;

      &:hover {
        background: #ebedf0;
      }
    }

    .qr-title {
      font-size: 18px;
      color: #323233;
      font-weight: 500;
      margin-bottom: 20px;
      margin-top: 10px;
    }

    .qr-username {
      font-size: 16px;
      color: #323233;
      margin-bottom: 30px;
    }

    .qr-code-container {
      margin-bottom: 30px;

      .qr-code-image {
        width: 189px;
        height: 189px;
        border-radius: 8px;
      }
    }

    .qr-tip {
      font-size: 14px;
      color: #969799;
      text-align: center;
    }
  }
}

// 固定底部的操作按钮
.fixed-bottom-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ebedf0;
  z-index: 100;
  height: 86px;

  .bottom-buttons {
    display: flex;
    height: 100%;

    .bottom-button {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;

      &:not(:last-child)::after {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 1px;
        height: 22px;
        background: #d1d5db;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }

      &:active {
        background: rgba(255, 255, 255, 0.2);
      }

      span {
        color: #1989fa;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}
</style>
